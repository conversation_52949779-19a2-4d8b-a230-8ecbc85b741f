<template>
  <view>
    <uv-navbar :title="title" :fixed="true" :placeholder="true" :autoBack="true" :bgColor="$c.themeColor()" leftIconColor="#ffffff" :titleStyle="{ color: '#fff' }"></uv-navbar>
    <view class="form-pannel">
      <uv-form labelWidth="90">
        <uv-form-item label="资产编号" borderBottom required v-if="autoBhCode == 0">
          <uv-input border="none" v-model="form.AssetNumber" :disabled="type == 'edit'" disabledColor="#fff" type="text" placeholder="请输入" />
        </uv-form-item>
        <uv-form-item label="资产名称" borderBottom required>
          <uv-input border="none" v-model="form.AssetName" type="text" placeholder="请输入" />
        </uv-form-item>
        <common-picker label="资产分类" pickerType="category" v-model="form.AssetCateCode" v-model:displayValue="form.AssetCateName" required :requireLeaf="true"></common-picker>

        <common-picker label="仓库" pickerType="warehouse" v-model="form.WarehouseCode" v-model:displayValue="form.WarehouseName"></common-picker>

        <common-picker label="管理部门" pickerType="dept" v-model="form.OrgCode" v-model:displayValue="form.OrgName" required></common-picker>

        <common-picker
          label="管理人"
          pickerType="user"
          userType="dept"
          v-model="form.ChargeUserCode"
          v-model:displayValue="form.ChargeUserName"
          :condition="{ field: '管理部门', value: form.OrgCode }"
          :watchValue="form.OrgCode"></common-picker>
        <common-picker
          label="使用部门"
          pickerType="dept"
          v-model="form.UseDeptCode"
          v-model:displayValue="form.UseDeptName"
          :extra="{ LoginUserCode: form.LoginUserCode }"></common-picker>

        <common-picker
          label="使用人"
          pickerType="user"
          userType="dept"
          v-model="form.UseUserCode"
          v-model:displayValue="form.UseUserName"
          :condition="{ field: '使用部门', value: form.UseDeptCode }"
          :watchValue="form.UseDeptCode"></common-picker>

        <common-picker required label="购置日期" pickerType="date" v-model="form.BuyDate"></common-picker>

        <common-picker label="启用日期" pickerType="date" v-model="form.UseDate"></common-picker>

        <uv-form-item required label="使用期限" borderBottom>
          <uv-input border="none" v-model="form.ScrapMonth" type="number" placeholder="请输入" />
          <view slot="right">月</view>
        </uv-form-item>

        <common-picker label="位置" pickerType="location" v-model="form.LocationCode" v-model:displayValue="form.LocationName"></common-picker>

        <uv-form-item label="规格型号" borderBottom>
          <uv-input border="none" v-model="form.Specification" type="text" placeholder="请输入" />
        </uv-form-item>

        <uv-form-item label="计量单位" borderBottom>
          <uv-input border="none" v-model="form.Unit" type="text" placeholder="请输入" />
        </uv-form-item>

        <common-picker label="供应商名称" pickerType="supplier" v-model="form.SupplierCode" v-model:displayValue="form.SupplierName"></common-picker>

        <common-picker label="品牌" pickerType="brand" v-model="form.BrandCode" v-model:displayValue="form.BrandName"></common-picker>

        <uv-form-item required label="资产价值" borderBottom>
          <uv-input border="none" v-model="form.AssetPrice" type="number" placeholder="请输入" />
        </uv-form-item>

        <common-picker label="来源" pickerType="source" v-model="form.SourceType" v-model:displayValue="form.SourceTypeCaption"></common-picker>

        <uv-form-item label="财务编号" borderBottom>
          <uv-input border="none" v-model="form.FinanceNumber" type="text" placeholder="请输入" />
        </uv-form-item>

        <common-picker required label="资产状态" pickerType="status" v-model="form.AssetStatus" v-model:displayValue="form.AssetStatusName"></common-picker>

        <uv-form-item label="备注" borderBottom>
          <uv-input border="none" v-model="form.Remark" type="text" placeholder="请输入" />
        </uv-form-item>

        <uv-form-item label="缩略图">
          <common-upload v-model="form.Smallimage" :maxCount="1"></common-upload>
        </uv-form-item>
      </uv-form>
    </view>
    <view class="fixbtn">
      <view class="blank"></view>
      <view class="btns bor-t flex">
        <view class="btn" @click="submit()">确定</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      title: "",
      form: {
        Code: "",
        AssetName: "",
        AssetCateCode: "",
        AssetCateName: "",
        AssetNumber: "",
        WarehouseCode: "",
        WarehouseName: "",
        OrgCode: "",
        OrgName: "",
        UseDeptCode: "",
        UseDeptName: "",
        UseUserCode: "",
        UseUserName: "",
        ChargeUserCode: "",
        ChargeUserName: "",
        BuyDate: "",
        UseDate: "",
        ScrapMonth: "0",
        LocationCode: "",
        LocationName: "",
        Specification: "",
        Unit: "",
        SupplierCode: "",
        SupplierName: "",
        BrandCode: "",
        BrandName: "",
        AssetPrice: "0",
        SourceType: "",
        SourceTypeCaption: "",
        FinanceNumber: "",
        AssetStatus: "",
        AssetStatusName: "",
        Smallimage: "",
        Remark: "",
        IsPrint: 0,
        LoginUserCode: uni.getStorageSync("UserCode"),
      },
      maxDate: new Date().getTime(),
      type: "",
      autoBhCode: 1, //是否自动编号，默认1
    };
  },
  onLoad(options) {
    this.title = options.type == "add" ? "增加资产" : "修改资产";
    this.type = options.type;
    if (options.type == "edit") {
      this.$apis.getAssetDetail({ AssetNumber: options.assetNumber, LoginUserCode: uni.getStorageSync("UserCode") }).then((res) => {
        for (let i in this.form) {
          if (res.data.hasOwnProperty(i)) {
            this.form[i] = res.data[i];
          } else {
            console.log(i);
          }
        }
        this.form.BuyDate = res.data.BuyDateCaption;
        this.form.Code = res.data.Code;
        this.form.AssetStatusName = res.data.AssetStatusCaption;
      });
    }

    //获取规则编码
    this.autoBhCode = this.$c.getRuleCode("ZCBH");
  },
  methods: {
    handleUploadChange(e) {
      this.form.Smallimage = e.urls;
    },
    submit() {
      // 检查必填字段
      if (this.autoBhCode == 0 && !this.form.AssetNumber) {
        return uni.$uv.toast("请输入资产编号");
      }
      if (!this.form.AssetName) {
        return uni.$uv.toast("请输入资产名称");
      }
      if (!this.form.AssetCateCode) {
        return uni.$uv.toast("请选择资产分类");
      }
      if (!this.form.OrgCode) {
        return uni.$uv.toast("请选择管理部门");
      }
      if (!this.form.BuyDate) {
        return uni.$uv.toast("请选择购置日期");
      }
      if (this.form.ScrapMonth === "" || this.form.ScrapMonth === null || isNaN(Number(this.form.ScrapMonth))) {
        return uni.$uv.toast("请输入使用期限");
      }
      if (this.form.AssetPrice === "" || this.form.AssetPrice === null || isNaN(Number(this.form.AssetPrice))) {
        return uni.$uv.toast("请输入资产价值");
      }
      if (this.form.AssetStatus === "" || this.form.AssetStatus === null || isNaN(Number(this.form.AssetStatus))) {
        return uni.$uv.toast("请选择资产状态");
      }
      console.log(this.form);
      // 验证通过，提交表单
      this.$apis[this.type == "add" ? "addAsset" : "editAsset"](this.form).then((res) => {
        if (res.code === 100) {
          uni.$uv.toast("提交成功");
          // 返回上一页
          setTimeout(() => {
            uni.navigateBack();
            if (this.type == "add") {
              uni.$emit("refreshList");
            }
            if (this.type == "edit") {
              uni.$emit("refreshDetail");
            }
          }, 1500);
        } else {
          uni.$uv.toast(res.msg || "提交失败");
        }
      });
    },
  },
};
</script>

<style scoped></style>
