<template>
  <view>
    <uv-navbar title="资产详情" :fixed="true" :placeholder="true" :autoBack="true" :bgColor="$c.themeColor()" leftIconColor="#ffffff" :titleStyle="{ color: '#fff' }"></uv-navbar>
    <view class="pd-info">
      <view class="top flext">
        <view class="flex-bd">
          <view class="name">{{ info.AssetName }}</view>
          <view class="no">
            资产编码:
            <view class="inline">{{ info.AssetNumber }}</view>
          </view>
          <view class="status">
            <view :class="['status-text', 'tag' + info.AssetStatus]">{{ info.AssetStatusCaption }}</view>
          </view>
        </view>
        <image class="cover" mode="aspectFill" @click="$c.viewImage(info.Smallimage)" v-if="info.Smallimage" :src="$c.getImageUrl(info.Smallimage)"></image>
        <image class="cover" v-else src="../../static/images/sbnopic.png"></image>
      </view>
      <view class="hr"></view>
      <view class="pannel">
        <view class="tit">基本信息</view>
        <view class="pd030">
          <uv-cell-group>
            <uv-cell title="资产分类" :value="info.AssetCateName"></uv-cell>
            <uv-cell title="规格型号" :value="info.Specification || '--'"></uv-cell>
            <uv-cell title="品牌" :value="info.BrandName || '--'"></uv-cell>
            <uv-cell title="供应商" :value="info.SupplierName || '--'"></uv-cell>
            <uv-cell title="单位" :value="info.Unit || '--'"></uv-cell>
            <uv-cell title="资产价值" :value="info.AssetPrice ? `¥${info.AssetPrice}` : '--'"></uv-cell>
            <uv-cell title="财务编号" :value="info.FinanceNumber || '--'"></uv-cell>
            <uv-cell title="RFID编码" :value="info.Rfid || '--'"></uv-cell>
            <uv-cell title="来源" :value="info.SourceTypeCaption || '--'"></uv-cell>
            <uv-cell title="是否打印" :value="info.IsPrintCaption || '--'"></uv-cell>
            <uv-cell title="使用部门" :value="info.UseDeptName || '--'"></uv-cell>
            <uv-cell title="使用人" :value="info.UseUserName || '--'"></uv-cell>
            <uv-cell title="管理人" :value="info.ChargeUserName || '--'"></uv-cell>
            <uv-cell title="管理部门" :value="info.OrgName || '--'"></uv-cell>
            <uv-cell title="位置" :value="info.LocationNamePathCaption || '--'"></uv-cell>
            <uv-cell title="仓库" :value="info.WarehouseName || '--'"></uv-cell>
            <uv-cell title="购置日期" :value="info.BuyDateCaption || '--'"></uv-cell>
            <uv-cell title="启用日期" :value="info.UseDateCaption || '--'"></uv-cell>
            <uv-cell title="使用期限" :value="info.ScrapMonth ? `${info.ScrapMonth}个月` : '--'" :border="false"></uv-cell>
          </uv-cell-group>
        </view>
      </view>
      <block v-if="exInfo.length > 0">
        <view class="hr"></view>
        <view class="pannel">
          <view class="tit">扩展属性</view>
          <view class="pd030">
            <uv-cell-group>
              <uv-cell v-for="(item, index) in exInfo" :key="index" :title="item.name" :value="item.value" :border="index != exInfo.length - 1"></uv-cell>
            </uv-cell-group>
          </view>
        </view>
      </block>
      <view class="hr"></view>
      <view class="pannel" v-if="hisList.length">
        <view class="tit bor-b flex">
          <view class="flex-bd">资产履历</view>
          <view class="more" @click="$c.navigateTo('../assets/hisList?code=' + info.Code)">更多</view>
        </view>
        <view class="his-line pd30">
          <view class="li flext" v-for="(item, index) in hisList">
            <view class="flex-hd">
              <view class="dot"></view>
              <view class="line" v-if="index !== hisList.length - 1"></view>
            </view>
            <view class="flex-bd">
              <view class="time">{{ $c.formatDate(item.CreateDateTime) }}</view>
              <view class="desc">
                <view class="p">
                  操作人员:
                  <view class="inline">{{ item.UserName }}</view>
                </view>
                <view class="p">操作内容: {{ item.OperateContent }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="fixbtn">
      <view class="blank"></view>
      <view class="btns bor-t flex">
        <view class="btn" @click="$c.navigateTo('../assets/add?assetNumber=' + info.AssetNumber + '&type=edit')">编辑</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      info: {},
      hisList: [],
      exInfo: [],
    };
  },
  onLoad(options) {
    this.getAssetDetail(options.assetNumber);

    uni.$on("refreshDetail", () => {
      this.getAssetDetail(this.info.AssetNumber);
    });
  },
  onUnload() {
    uni.$off("refreshDetail");
  },
  methods: {
    getAssetDetail(assetNumber) {
      this.$apis.getAssetDetail({ AssetNumber: assetNumber, LoginUserCode: uni.getStorageSync("UserCode") }).then((res) => {
        this.info = res.data;
        this.getAssetHistoryList(res.data.Code);
        this.getExtendPropertyList();
      });
    },
    getAssetHistoryList(code) {
      this.$apis.getAssetHistoryList({ Code: code }).then((res) => {
        var arr = [];
        for (let i in res.data) {
          if (i < 3) {
            arr.push(res.data[i]);
          }
        }
        this.hisList = arr;
      });
    },
    //获取扩展属性
    getExtendPropertyList() {
      this.$apis.getExtendPropertyList().then((res) => {
        for (let i in res.data) {
          if (this.info[res.data[i].Code]) {
            this.exInfo.push({
              name: res.data[i].AttributeName,
              value: this.info[res.data[i].Code],
            });
          }
        }
        console.log(this.exInfo);
      });
    },
  },
};
</script>

<style></style>
