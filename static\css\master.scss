$color-main: #1677ff;
$color-main-light: #5ba0ff;
$color-main-lighter: #b2cbf4;
$color-main-lightest: #f5f8fd;
/*辅助色*/
$color-danger: #f53f3f;
$color-danger-light: #f87878;
$color-danger-lighter: #fcc5c5;
$color-danger-lightest: #fef5f5;
/*警告色*/
$color-warning: #ff7d00;
$color-warning-light: #ffa44c;
$color-warning-lighter: #ffd8b2;
$color-warning-lightest: #fff8f2;
/*成功色*/
$color-success: #00b42a;
$color-success-light: #4cca69;
$color-success-lighter: #b2e8bf;
$color-success-lightest: #f2fbf4;
/*黄色*/
$color-yellow: #ffbb00;
$color-yellow-light: #ffcf4c;
$color-yellow-lighter: #fff7e1;
$color-yellow-lightest: #fffcf5;
/*基础色*/
$color-white: #ffffff;
$color-black: #000000;
$color-gray: #e2e2e2;
$color-gray-nomal: #5f5f5f;
$color-gray-nomaler: #dddddd;
$color-gray-light: #eeeeee;
$color-gray-lighter: #f6f6f6;
$color-gray-lightest: #fafafa;
$color-gray-dark: #d2d2d2;
$color-gray-darker: #c2c2c2;
$color-gray-darkest: #333333;
$color-gray-master: #999999;
$color-gray-mastest: #666666;
/*中性色*/
$color-base: #2f363c;
$color-base-darker: #23292e;
$color-base-darkest: #1c1f23;
body,
uni-page-body,
page {
  background-color: #f5f8fc;
}
.inline-block {
  display: inline-block;
}
.navbar-height {
  height: 88rpx;
}
.flex {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
}
.flex-t {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
}
.flex-b {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: flex-end;
  -webkit-box-align: flex-end;
  -webkit-align-items: flex-end;
}
.flex-item {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  min-width: 0;
}
.flex-space-between {
  justify-content: space-between;
}
.glass-effect {
  background: rgba(255, 255, 255, 0.5); /* 半透明白色背景 */
  backdrop-filter: blur(10px); /* 添加模糊效果 */
  border-radius: 20rpx; /* 可选，增加圆角 */
  padding: 20rpx; /* 内边距 */
}
/*资产管理*/
.fixed-tab-height {
  height: 44px;
}

.fixed-tab-height .fixed-tab {
  width: 100%;
  top: 80rpx;
  background: #fff;
  position: fixed;
  z-index: 2;
  height: 44px;
}

.scroll-content {
  position: relative;
}

.scroll-content .head {
  white-space: nowrap;
}

.scroll-content ._head {
  background: #f6f6f6;
  padding: 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  position: fixed;
  background: #fff;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 99;
}

.scroll-content ._head .i {
  display: inline-block;
}

.scroll-content ._head .i .icon {
  margin: 0 10rpx;
}

.top-search {
  width: 100%;
  box-sizing: border-box;
  padding: 8px 20rpx;
}

.top-search .flex-hd {
  margin-left: 20rpx;
}

.top-search .flex-hd i {
  font-size: 42rpx;
}

.hr {
  height: 16rpx;
  background: #f6f6f6;
  width: 100%;
}

.assets-list .item {
  padding: 20rpx;
}

.assets-list .item .cover {
  width: 100rpx;
  height: 100rpx;
}

.assets-list .item .img {
  margin-right: 20rpx;
  position: relative;
}

.assets-list .item .img .tag {
  font-size: 20rpx;
  position: absolute;
  bottom: 0;
  right: 0;
  color: #fff;
  padding: 0 2px;
}

.assets-list .item .img .tag0 {
  background: $color-success;
}

.assets-list .item .img .tag1 {
  background: $color-main;
}

.assets-list .item .img .tag2 {
  background: $color-danger;
}

.assets-list .item .img .tag5 {
  background: $color-gray-light;
}

.assets-list .item .img .tag3 {
  background: $color-warning;
}

.assets-list .item .name {
  font-size: 30rpx;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.assets-list .item .no {
  font-size: 26rpx;
  color: #999;
}
