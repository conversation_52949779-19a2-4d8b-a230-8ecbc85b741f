<template>
  <view>
    <uv-navbar title="用户信息" :fixed="true" :placeholder="true" :autoBack="true" :bgColor="$c.themeColor()" :titleStyle="{ color: '#fff' }">
      <template v-slot:left><view></view></template>
    </uv-navbar>
    <view class="user-main" style="margin-top: 20rpx">
      <view class="u-cells">
        <view class="cell bor-b">
          <view class="cell-content">单位名称</view>
          <view class="cell-ft">
            {{ UserInfo.orgname }}
          </view>
        </view>
        <view class="cell bor-b">
          <view class="cell-content">所属部门</view>
          <view class="cell-ft">
            {{ UserInfo.DeptName }}
          </view>
        </view>
        <view class="cell bor-b">
          <view class="cell-content">用户名</view>
          <view class="cell-ft">
            {{ UserInfo.username }}
          </view>
        </view>
        <view class="cell">
          <view class="cell-content">登录账号</view>
          <view class="cell-ft">
            {{ UserInfo.loginname }}
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      UserInfo: uni.getStorageSync("UserInfo"),
    };
  },
  onLoad() {},
  methods: {},
};
</script>

<style>
page {
  background: #f6f6f6;
}
</style>
