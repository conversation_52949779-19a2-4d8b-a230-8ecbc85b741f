import Request from "@/common/luch-request/index.js";

const http = new Request();
let loading = 0;
let isGettingToken = false;
let tokenRequests = [];
const needToken = false; // 根据实际需求设置是否需要token

// API 基础配置
let defaultUrl = "";
let staticUrl = "";

// 根据环境设置基础URL
if (process.env.NODE_ENV === "production") {
  staticUrl = "http://58.221.13.198:5100";
  defaultUrl = staticUrl + "/api/FAmsWebApi";
} else {
  staticUrl = "http://58.221.13.198:5100";
  defaultUrl = staticUrl + "/api/FAmsWebApi";
}

// 获取Token方法
function getToken() {
  return new Promise((resolve, reject) => {
    uni.request({
      url: staticUrl + "/api/token",
      method: "GET",
      data: {
        AppId: "a",
        AppSecret: "b",
      },
      header: {
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
      },
      success: (res) => {
        resolve(res);
      },
      fail: (err) => {
        reject(err);
      },
    });
  });
}

// 设置Token方法
http.setToken = (obj) => {
  http.config.header = {
    Authorization: "Bearer " + obj.token,
  };
  uni.setStorageSync("access_token", obj.token);
  uni.setStorageSync("token_time", new Date().getTime());
};

// 设置全局配置
http.setConfig((config) => {
  config.baseURL = defaultUrl;
  config.staticUrl = staticUrl;
  config.custom = {
    loading: true,
  };
  return config;
});

// 请求拦截器
http.interceptors.request.use(
  (config) => {
    // 日志记录
    //console.log(`${config.url} 请求:`, JSON.parse(JSON.stringify(config.data ? config.data : {})));

    // 显示加载提示
    if (config.custom.loading) {
      if (loading < 1) {
        uni.showLoading({
          title: "加载中",
          mask: true,
        });
      }
      loading++;
    }

    // 跳过H5Token接口的token验证
    if (config.url.indexOf("/H5Token") >= 0) {
      return config;
    }

    // Token处理逻辑
    if (needToken) {
      const now = new Date().getTime();
      const accessToken = uni.getStorageSync("access_token");
      const tokenTime = uni.getStorageSync("token_time");
      const isTokenExpired = !accessToken || now > tokenTime + 3600 * 24 * 1000;

      if (isTokenExpired) {
        if (!isGettingToken) {
          isGettingToken = true;

          getToken()
            .then((res) => {
              const token = res.data;
              http.setToken({ token });
              isGettingToken = false;
              return token;
            })
            .then((token) => {
              console.log("获取token成功，执行队列");
              tokenRequests.forEach((cb) => cb(token));
              tokenRequests = [];
            })
            .catch((err) => {
              console.error("获取token失败:", err);
              isGettingToken = false;
            });
        }

        return new Promise((resolve) => {
          tokenRequests.push((token) => {
            config.header = {
              Authorization: "Bearer " + token,
            };
            resolve(config);
          });
        });
      } else {
        config.header = {
          Authorization: "Bearer " + accessToken,
        };
      }
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
http.interceptors.response.use(
  (response) => {
    // 隐藏加载提示
    if (response.config.custom.loading) {
      loading--;
      if (loading <= 0) {
        loading = 0;
        uni.hideLoading();
      }
    }

    // 日志记录
    //console.log(`${response.config.url} 返回:`, JSON.parse(JSON.stringify(response.data)));

    // 处理401未授权的情况
    if (response.statusCode === 401) {
      // 清除本地token
      uni.removeStorageSync("access_token");
      uni.removeStorageSync("token_time");

      // 提示用户
      uni.showToast({
        title: "登录已过期，请重新登录",
        icon: "none",
        duration: 2000,
      });

      // 可以在这里添加跳转到登录页的逻辑
      setTimeout(() => {
        uni.navigateTo({
          url: "/pages/login/login",
        });
      }, 1500);

      return Promise.reject(new Error("登录已过期"));
    }

    return response.data;
  },
  (error) => {
    // 隐藏加载提示
    loading = 0;
    uni.hideLoading();

    // 处理401错误
    if (error && error.statusCode === 401) {
      // 清除本地token
      uni.removeStorageSync("access_token");
      uni.removeStorageSync("token_time");

      uni.showToast({
        title: "登录已过期，请重新登录",
        icon: "none",
        duration: 2000,
      });

      // 可以在这里添加跳转到登录页的逻辑
      setTimeout(() => {
        uni.navigateTo({
          url: "/pages/login/login",
        });
      }, 1500);

      return Promise.reject(new Error("登录已过期"));
    }

    // 通用错误提示
    uni.showToast({
      icon: "none",
      title: "请求错误，请稍后再试",
      duration: 2000,
    });

    return Promise.reject(error);
  }
);

export default http;
