//#ifdef H5
//uniapp微信公众号（h5）端跳转微信小程序
//import wx from "jweixin-module"

//#endif
import http from "../common/http/index.js";
import apis from "../common/apis.js";
var common = {};

common.formateTime = function (e) {
  if (e) {
    e = e.replace(/\//g, "-");

    return uni.$u.timeFormat(new Date(e).getTime(), "yyyy-mm-dd");
  }
};

common.navigateTo = function (url) {
  uni.navigateTo({
    url: url,
  });
};

common.wxApi = function (url) {
  http
    .post(
      "/GetScanSign",
      {
        url: url,
      },
      { custom: { loading: false } }
    )
    .then((res) => {
      wx.config({
        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
        appId: "wxeedc53e4628dafa8", // 必填，公众号的唯一标识，填自己的！
        timestamp: res.Data.timestamp, // 必填，生成签名的时间戳，刚才接口拿到的数据
        nonceStr: res.Data.noncestr, // 必填，生成签名的随机串
        signature: res.Data.signature, // 必填，签名，见附录1
        jsApiList: ["scanQRCode"],
      });
    });
};

common.getImageUrl = function (url) {
  if (url) {
    return http.config.staticUrl + url;
  }
};

common.viewImage = function (url, index = 0) {
  // 处理空值情况
  if (!url) {
    return;
  }

  // 将逗号分隔的字符串转为数组
  if (typeof url === "string" && url.includes(",")) {
    url = url.split(",").filter((item) => item);
  }

  // 统一转换为数组处理
  const urls = Array.isArray(url) ? url : [url];

  // 处理空数组情况
  if (urls.length === 0) {
    return;
  }

  // 为每个url添加前缀
  const formattedUrls = urls
    .map((item) => {
      // 去除可能存在的空格
      item = item.trim();
      // 只有非空值才添加前缀
      return item ? common.getImageUrl(item) : "";
    })
    .filter((item) => item); // 过滤掉空值

  // 处理没有有效图片的情况
  if (formattedUrls.length === 0) {
    return;
  }

  // 确保 index 在有效范围内
  const currentIndex = Math.min(Math.max(0, index), formattedUrls.length - 1);

  uni.previewImage({
    urls: formattedUrls,
    current: currentIndex,
  });
};

common.formatDate = function (dateStr) {
  if (!dateStr) return "";

  const date = new Date(dateStr);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hour = String(date.getHours()).padStart(2, "0");
  const minute = String(date.getMinutes()).padStart(2, "0");
  const second = String(date.getSeconds()).padStart(2, "0");

  return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
};

common.themeColor = function () {
  return "#1677ff";
};

/**
 * 上传单个文件
 * @param {Object} file - 文件对象
 * @returns {Promise} 上传结果
 */
common.uploadFilePromise = function (file) {
  console.log("开始上传文件:", file);
  return new Promise((resolve, reject) => {
    http
      .upload(
        "/FamsApp/UploadFile",
        {
          filePath: file.url || file,
          name: "file",
        },
        { custom: { loading: false } }
      )
      .then((res) => {
        console.log("上传成功响应:", res);
        if (res.ServerRelativeFileName) {
          resolve(res);
        } else {
          console.error("上传响应缺少必要字段:", res);
          reject(new Error("Invalid response format"));
        }
      })
      .catch((err) => {
        console.error("上传失败:", err);
        reject(err);
      });
  });
};

/**
 * 处理u-upload组件的afterRead事件
 * @param {Object} event - 上传事件对象
 * @param {Object} fileList - 文件列表引用
 * @returns {Promise} 处理结果
 */
common.handleUploadAfterRead = async function (event, fileList) {
  // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
  const lists = [].concat(event.file);

  // 先添加文件到列表，状态为上传中
  lists.forEach((item) => {
    fileList.push({
      ...item,
      status: "uploading",
      message: "上传中",
    });
  });

  // 记录开始的索引位置
  const startIndex = fileList.length - lists.length;

  // 逐个上传文件
  for (let i = 0; i < lists.length; i++) {
    try {
      const result = await this.uploadFilePromise(lists[i]);

      const currentIndex = startIndex + i;

      if (result && result.ServerRelativeFileName) {
        console.log(result);
        // 更新对应索引的文件状态为成功
        fileList[currentIndex] = {
          ...fileList[currentIndex],
          status: "success",
          message: "",
          url: http.config.staticURL + result.ServerRelativeFileName,
          pic: result.ServerRelativeFileName,
        };
      } else {
        throw new Error("上传返回数据异常");
      }
    } catch (err) {
      console.error("Upload failed:", err);
      const currentIndex = startIndex + i;
      if (fileList[currentIndex]) {
        fileList[currentIndex].status = "error";
        fileList[currentIndex].message = "上传失败";
      }
      uni.showToast({
        title: "上传失败",
        icon: "none",
      });
    }
  }
};

/**
 * 处理u-upload组件的delete事件
 * @param {Object} event - 删除事件对象
 * @param {Object} fileList - 文件列表引用
 */
common.handleUploadDelete = function (event, fileList) {
  fileList.splice(event.index, 1);
};

common.submitAsset = function (code, api) {
  uni.showModal({
    title: "提示",
    content: "确定提交吗？",
    success: (res) => {
      if (res.confirm) {
        apis[api]({
          Code: code,
          LoginUserCode: uni.getStorageSync("UserCode"),
        }).then((res) => {
          if (res.code == 100) {
            uni.showToast({
              title: "提交成功",
            });
            setTimeout(() => {
              uni.navigateBack();
              uni.$emit("refreshList");
            }, 1500);
          } else {
            this.$u.toast(res.msg);
          }
        });
      }
    },
  });
};

//判断是否pda
common.isPda = function () {
  console.log(uni.getSystemInfoSync());
  return uni.getSystemInfoSync().model == "k62v1_6c" || uni.getSystemInfoSync().model == "common";
};

common.isPda1 = function () {
  return uni.getSystemInfoSync().model == "k62v1_6c";
};

common.isPda2 = function () {
  return uni.getSystemInfoSync().model == "common";
};
//获取规则编码
common.getRuleCode = function (code) {
  var arr = uni.getStorageSync("ruleCode");
  for (let i in arr) {
    if (arr[i].code == code) {
      return arr[i].isAuto;
    }
  }
  return 1;
};

common.selectAsset = function (e) {
  if (e) {
    var url = "";
    for (let i in e) {
      if (!e[i].value && e[i].message) {
        return uni.showToast({
          icon: "none",
          title: e[i].message,
        });
      }
      if (i > 0) {
        url += "&";
      }
      url += e[i].key + "=" + e[i].value;
    }
    uni.navigateTo({
      url: "../assets/select?" + url,
    });
  } else {
    uni.navigateTo({
      url: "../assets/select",
    });
  }
};
export default common;
