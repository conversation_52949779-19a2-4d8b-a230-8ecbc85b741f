{"easycom": {"^u-(.*)": "uview-ui/components/u-$1/u-$1.vue"}, "pages": [{"path": "pages/index/index", "style": {"navigationStyle": "custom", "app-plus": {"bounce": "none"}}}, {"path": "pages/login/login", "style": {"navigationStyle": "custom", "app-plus": {"bounce": "none"}}}, {"path": "pages/user/user", "style": {"navigationStyle": "custom", "app-plus": {"bounce": "none"}}}, {"path": "pages/charts/charts", "style": {"navigationStyle": "custom", "app-plus": {"bounce": "none"}}}, {"path": "pages/assets/list", "style": {"navigationStyle": "custom", "app-plus": {"bounce": "none"}}}, {"path": "pages/assets/detail", "style": {"navigationStyle": "custom", "app-plus": {"bounce": "none"}}}, {"path": "pages/assets/add", "style": {"navigationStyle": "custom", "app-plus": {"bounce": "none"}}}, {"path": "pages/assets/select", "style": {"navigationStyle": "custom", "app-plus": {"bounce": "none"}}}, {"path": "pages/assets/hisList", "style": {"navigationStyle": "custom", "app-plus": {"bounce": "none"}}}, {"path": "pages/assetsReceipt/list", "style": {"navigationStyle": "custom", "app-plus": {"bounce": "none"}}}, {"path": "pages/assetsReceipt/add", "style": {"navigationStyle": "custom", "app-plus": {"bounce": "none"}}}, {"path": "pages/assetsReceipt/detail", "style": {"navigationStyle": "custom", "app-plus": {"bounce": "none"}}}, {"path": "pages/assetsChange/list", "style": {"navigationStyle": "custom", "app-plus": {"bounce": "none"}}}, {"path": "pages/assetsChange/add", "style": {"navigationStyle": "custom", "app-plus": {"bounce": "none"}}}, {"path": "pages/assetsChange/detail", "style": {"navigationStyle": "custom", "app-plus": {"bounce": "none"}}}, {"path": "pages/userInfo/userInfo", "style": {"navigationStyle": "custom", "app-plus": {"bounce": "none"}}}, {"path": "pages/assetsCheck/list", "style": {"navigationStyle": "custom", "app-plus": {"bounce": "none"}}}, {"path": "pages/assetsCheck/checkList", "style": {"navigationStyle": "custom", "app-plus": {"bounce": "none"}}}, {"path": "pages/assetsCheck/detail", "style": {"navigationStyle": "custom", "app-plus": {"bounce": "none"}}}, {"path": "pages/assetsCheck/checkDetail", "style": {"navigationStyle": "custom", "app-plus": {"bounce": "none"}}}, {"path": "pages/audit/list", "style": {"navigationStyle": "custom", "app-plus": {"bounce": "none"}}}, {"path": "pages/assetsProfit/list", "style": {"navigationStyle": "custom", "app-plus": {"bounce": "none"}}}, {"path": "pages/assetsProfit/add", "style": {"navigationStyle": "custom", "app-plus": {"bounce": "none"}}}, {"path": "pages/editPwd/editPwd", "style": {"navigationStyle": "custom", "app-plus": {"bounce": "none"}}}, {"path": "pages/fileDetail/fileDetail", "style": {"navigationStyle": "custom", "app-plus": {"bounce": "none"}}}, {"path": "pages/test/test", "style": {"navigationBarTitleText": ""}}], "globalStyle": {"navigationBarTextStyle": "white", "navigationBarTitleText": "安友标准UI", "navigationBarBackgroundColor": "#1677ff", "backgroundColor": "#1677ff", "navigationStyle": "custom"}, "uniIdRouter": {}, "tabBar": {}}