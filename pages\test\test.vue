<template>
  <view class="content_row">
    <text class="text-area">{{ result }}</text>
    <button @click="Clicked">连接</button>
    <button @click="Unlink">断开</button>
  </view>
  <view class="content_row">
    <text class="text-area">{{ version }}</text>
    <button @click="GetVersion">获取固件版本：</button>
  </view>
  <view class="content_row">
    <text class="text-area">{{ tempture }}摄氏度</text>
    <button @click="GetTempture">获取温度：</button>
  </view>
  <view class="content_row">
    <text>{{ msg }}</text>
    <button @click="Inventory">盘存</button>
  </view>
  <view class="content_row">
    <text>{{ Power }}</text>
    <button @click="SetPower">设置功率</button>
    <button @click="GetPower">获取功率</button>
  </view>
</template>

<script>
const testModule = uni.requireNativePlugin("uniplugin_rfid_module");

export default {
  // 接收父组件传递过来的蓝牙插件对象
  props: {
    testModule: {
      type: Object,
      require: true,
    },
  },
  data() {
    return {
      title: "Hello",
      result: "Result",
      version: "0.0.0",
      tempture: "0",
      msg: "Inventory Message",
      Power: 0,
      count: 0,
      arrDate: [], //缓存
      isPlay: false,
      innerAudioContext: null,
      isStop: false,
    };
  },
  onLoad() {
    this.innerAudioContext = uni.createInnerAudioContext();
    this.innerAudioContext.autoplay = false;
    this.innerAudioContext.loop = true;
    this.innerAudioContext.src = "/static/music.mp3";
    this.innerAudioContext.onPlay(() => {
      console.log("开始播放");
    });
    this.innerAudioContext.onError((res) => {
      console.log(res.errMsg);
      console.log(res.errCode);
    });

    // // 获取音频管理对象
    // const audio = plus.audio.createPlayer("/static/music.mp3");
    // // 高级配置
    // audio.setSessionCategory("playback"); // 设置音频会话模式
    // audio.setRoute("speaker"); // 强制扬声器输出

    // // 播放控制
    // audio.play(
    //   () => console.log("播放成功"),
    //   (err) => console.error(err)
    // );

    if (testModule == null) {
      uni.showToast({
        icon: "error",
        title: "插件为NULL",
      });
      this.result = "插件为NULL";
    }
    // testModule.TestAdd(
    //   {
    //     a: 5,
    //     b: 20,
    //   },
    //   (res) => {
    //     uni.showToast({
    //       icon: "success",
    //       title: JSON.stringify(res),
    //     });
    //     this.result = JSON.stringify(res);
    //   }
    // );

    plus.key.addEventListener("keydown", (e) => {
      //console.log(e);
      if (e.keyCode === 134) {
        this.isStop = false;
        uni.showToast({
          icon: "none",
          title: "按下按键" + this.count,
        });
        if (this.count % 50 == 0) {
          this.Inventory()
        }
        this.count++;
      }
    });

    plus.key.addEventListener("keyup", (e) => {
      console.log(e);
      if (e.keyCode === 134) {
        this.isStop = true;
      }
    });
  },
  methods: {
    GetDate() {},
    Save() {},
    Unlink() {
      testModule.Disconnect((res) => {
        uni.showToast({
          icon: "none",
          title: JSON.stringify(res),
        });
      });
    },
    Clicked() {
      testModule.Connect(
        {
          com: "/dev/ttyAS3",
          baud: 115200,
        },
        (res) => {
          uni.showToast({
            icon: "none",
            title: JSON.stringify(res),
          });
          this.result = JSON.stringify(res);

          //设置蜂鸣器
          setTimeout(() => {
            testModule.SetCmd(
              {
                FunName: "setBeeperMode",
                Beeper: "BuzzerFoundTag",
              },
              (res) => {
                console.log(res);
              }
            );
          }, 500);
        }
      );
    },
    GetVersion() {
      testModule.GetCmd(
        {
          FunName: "getFirmwareVersion",
        },
        (res) => {
          uni.showToast({
            icon: "none",
            title: JSON.stringify(res),
          });
          var ver = JSON.parse(JSON.stringify(res));
          console.log(JSON.stringify(res).replace('\\"', "").replace('\\"', ""));
          console.log(" version:= " + ver.version);
          console.log(" ChipType:= " + ver.chipType);
          console.log(" cmd:= " + ver.cmd);
          this.version = ver.version;
        }
      );
    },
    GetTempture() {
      testModule.GetCmd(
        {
          FunName: "getReaderTemperature",
        },
        (res) => {
          uni.showToast({
            icon: "none",
            title: JSON.stringify(res),
          });
          var temp = JSON.parse(JSON.stringify(res));
          this.tempture = temp.temperature;
        }
      );
    },
    SetPower() {
      testModule.SetCmd(
        {
          FunName: "setOutputPower",
          power: 25,
          isTemp: true,
        },
        (res) => {
          uni.showToast({
            icon: "none",
            title: JSON.stringify(res),
          });
        }
      );
    },
    GetPower() {
      testModule.GetCmd(
        {
          FunName: "getOutputPower",
        },
        (res) => {
          uni.showToast({
            icon: "none",
            title: JSON.stringify(res),
          });
          var pwr = JSON.parse(JSON.stringify(res));
          this.Power = pwr.outputPower;
        }
      );
    },
    async Inventory() {
      console.log("Inventory");
      testModule.SetCmd(
        {
          FunName: "startInventory",
          fastSwitchMode: false,
          session: 1,
          target: 0,
          Phase: false,
        },
        (res) => {
          console.log("1." + JSON.stringify(res));
          var InventoryData = JSON.parse(JSON.stringify(res));
          this.msg = InventoryData.inventoryTag;
        }
      );
    },
  },
};
</script>

<style>
.content_row {
  padding: 30rpx;
}

.content_row {
  margin-top: 30rpx;
}
button {
  margin-top: 30rpx;
}
</style>
