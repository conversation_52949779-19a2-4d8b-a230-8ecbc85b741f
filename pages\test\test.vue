<template>
  <view class="content_row">
    <text class="text-area">{{ result }}</text>
    <button @click="Clicked">连接</button>
    <button @click="Unlink">断开</button>
  </view>
  <view class="content_row">
    <text class="text-area">{{ version }}</text>
    <button @click="GetVersion">获取固件版本：</button>
  </view>
  <view class="content_row">
    <text class="text-area">{{ tempture }}摄氏度</text>
    <button @click="GetTempture">获取温度：</button>
  </view>
  <view class="content_row">
    <text>{{ msg }}</text>
    <button @click="Inventory">盘存</button>
  </view>
  <view class="content_row">
    <text>{{ Power }}</text>
    <button @click="SetPower">设置功率</button>
    <button @click="GetPower">获取功率</button>
  </view>
</template>

<script>
const testModule = uni.requireNativePlugin("uniplugin_rfid_module");

export default {
  // 接收父组件传递过来的蓝牙插件对象
  props: {
    testModule: {
      type: Object,
      require: true,
    },
  },
  data() {
    return {
      title: "Hello",
      result: "Result",
      version: "0.0.0",
      tempture: "0",
      msg: "Inventory Message",
      Power: 0,
      count: 0,
      arrDate: [], //缓存扫码数据
      isPlay: false,
      innerAudioContext: null,
      isStop: false,
      lastScanTime: 0, // 上次扫码时间
      scanDebounceTime: 300, // 防抖时间(毫秒)
      isScanning: false, // 是否正在扫码
    };
  },
  onLoad() {
    // 初始化音频上下文
    this.innerAudioContext = uni.createInnerAudioContext();
    this.innerAudioContext.autoplay = false;
    this.innerAudioContext.loop = false; // 改为不循环播放
    this.innerAudioContext.src = "/static/music.mp3";

    this.innerAudioContext.onPlay(() => {
      console.log("开始播放扫码成功音效");
    });

    this.innerAudioContext.onEnded(() => {
      console.log("音效播放结束");
      this.isPlay = false;
    });

    this.innerAudioContext.onError((res) => {
      console.error("音频播放错误:", res.errMsg, res.errCode);
      this.isPlay = false;
    });

    // // 获取音频管理对象
    // const audio = plus.audio.createPlayer("/static/music.mp3");
    // // 高级配置
    // audio.setSessionCategory("playback"); // 设置音频会话模式
    // audio.setRoute("speaker"); // 强制扬声器输出

    // // 播放控制
    // audio.play(
    //   () => console.log("播放成功"),
    //   (err) => console.error(err)
    // );

    if (testModule == null) {
      uni.showToast({
        icon: "error",
        title: "插件为NULL",
      });
      this.result = "插件为NULL";
    }
    // testModule.TestAdd(
    //   {
    //     a: 5,
    //     b: 20,
    //   },
    //   (res) => {
    //     uni.showToast({
    //       icon: "success",
    //       title: JSON.stringify(res),
    //     });
    //     this.result = JSON.stringify(res);
    //   }
    // );

    plus.key.addEventListener("keydown", (e) => {
      if (e.keyCode === 134) {
        this.handleScanKeyPress();
      }
    });

    plus.key.addEventListener("keyup", (e) => {
      if (e.keyCode === 134) {
        this.handleScanKeyRelease();
      }
    });
  },
  methods: {
    // 处理扫码按键按下
    handleScanKeyPress() {
      const currentTime = Date.now();

      // 防抖处理：如果距离上次扫码时间太短，则忽略
      if (currentTime - this.lastScanTime < this.scanDebounceTime) {
        return;
      }

      this.isStop = false;
      this.count++;

      // 如果没有正在扫码，则开始扫码
      if (!this.isScanning) {
        this.isScanning = true;
        this.lastScanTime = currentTime;
        this.performScan();
      }
    },

    // 处理扫码按键释放
    handleScanKeyRelease() {
      this.isStop = true;
      this.isScanning = false;
    },

    // 执行扫码操作
    async performScan() {
      try {
        await this.Inventory();
      } catch (error) {
        console.error('扫码失败:', error);
        this.isScanning = false;
      }
    },

    // 检查数据是否重复
    isDuplicateData(data) {
      if (!data || data.trim() === '') {
        return true; // 空数据视为重复
      }
      return this.arrDate.includes(data);
    },

    // 添加数据到缓存
    addToCache(data) {
      if (data && data.trim() !== '' && !this.isDuplicateData(data)) {
        this.arrDate.push(data);
        // 限制缓存大小，避免内存溢出
        if (this.arrDate.length > 1000) {
          this.arrDate.shift(); // 移除最旧的数据
        }
        return true; // 新数据
      }
      return false; // 重复或无效数据
    },

    // 播放扫码成功音效
    playScanSuccessSound() {
      if (this.innerAudioContext && !this.isPlay) {
        this.isPlay = true;
        this.innerAudioContext.play();

        // 播放一段时间后停止
        setTimeout(() => {
          this.innerAudioContext.pause();
          this.isPlay = false;
        }, 500); // 播放500毫秒
      }
    },

    // 清空扫码缓存
    clearScanCache() {
      this.arrDate = [];
      uni.showToast({
        icon: "success",
        title: "缓存已清空",
        duration: 1000
      });
      console.log("扫码缓存已清空");
    },

    GetDate() {},
    Save() {},
    Unlink() {
      testModule.Disconnect((res) => {
        uni.showToast({
          icon: "none",
          title: JSON.stringify(res),
        });
      });
    },
    Clicked() {
      testModule.Connect(
        {
          com: "/dev/ttyAS3",
          baud: 115200,
        },
        (res) => {
          uni.showToast({
            icon: "none",
            title: JSON.stringify(res),
          });
          this.result = JSON.stringify(res);

          //设置蜂鸣器
          setTimeout(() => {
            testModule.SetCmd(
              {
                FunName: "setBeeperMode",
                Beeper: "BuzzerFoundTag",
              },
              (res) => {
                console.log(res);
              }
            );
          }, 500);
        }
      );
    },
    GetVersion() {
      testModule.GetCmd(
        {
          FunName: "getFirmwareVersion",
        },
        (res) => {
          uni.showToast({
            icon: "none",
            title: JSON.stringify(res),
          });
          var ver = JSON.parse(JSON.stringify(res));
          console.log(JSON.stringify(res).replace('\\"', "").replace('\\"', ""));
          console.log(" version:= " + ver.version);
          console.log(" ChipType:= " + ver.chipType);
          console.log(" cmd:= " + ver.cmd);
          this.version = ver.version;
        }
      );
    },
    GetTempture() {
      testModule.GetCmd(
        {
          FunName: "getReaderTemperature",
        },
        (res) => {
          uni.showToast({
            icon: "none",
            title: JSON.stringify(res),
          });
          var temp = JSON.parse(JSON.stringify(res));
          this.tempture = temp.temperature;
        }
      );
    },
    SetPower() {
      testModule.SetCmd(
        {
          FunName: "setOutputPower",
          power: 25,
          isTemp: true,
        },
        (res) => {
          uni.showToast({
            icon: "none",
            title: JSON.stringify(res),
          });
        }
      );
    },
    GetPower() {
      testModule.GetCmd(
        {
          FunName: "getOutputPower",
        },
        (res) => {
          uni.showToast({
            icon: "none",
            title: JSON.stringify(res),
          });
          var pwr = JSON.parse(JSON.stringify(res));
          this.Power = pwr.outputPower;
        }
      );
    },
    async Inventory() {
      console.log("开始扫码...");

      return new Promise((resolve, reject) => {
        testModule.SetCmd(
          {
            FunName: "startInventory",
            fastSwitchMode: false,
            session: 1,
            target: 0,
            Phase: false,
          },
          (res) => {
            try {
              console.log("扫码结果: " + JSON.stringify(res));
              const inventoryData = JSON.parse(JSON.stringify(res));
              const tagData = inventoryData.inventoryTag;

              // 更新显示消息
              this.msg = tagData || "无数据";

              // 检查是否有有效数据
              if (tagData && tagData.trim() !== '') {
                // 检查是否为新数据
                const isNewData = this.addToCache(tagData);

                if (isNewData) {
                  console.log("发现新标签: " + tagData);
                  // 播放成功音效
                  this.playScanSuccessSound();

                  uni.showToast({
                    icon: "success",
                    title: "扫码成功",
                    duration: 1000
                  });
                } else {
                  console.log("重复标签: " + tagData);
                  uni.showToast({
                    icon: "none",
                    title: "重复数据",
                    duration: 1000
                  });
                }
              } else {
                console.log("未扫描到标签");
                // 无数据时不播放音效
              }

              this.isScanning = false;
              resolve(inventoryData);

            } catch (error) {
              console.error("处理扫码结果失败:", error);
              this.isScanning = false;
              reject(error);
            }
          }
        );
      });
    },
  },
};
</script>

<style>
.content_row {
  padding: 30rpx;
}

.content_row {
  margin-top: 30rpx;
}
button {
  margin-top: 30rpx;
}
</style>
