$color-main: #1677ff;
$color-main-light: #5ba0ff;
$color-main-lighter: #b2cbf4;
$color-main-lightest: #f5f8fd;
/*辅助色*/
$color-danger: #f53f3f;
$color-danger-light: #f87878;
$color-danger-lighter: #fcc5c5;
$color-danger-lightest: #fef5f5;
/*警告色*/
$color-warning: #ff7d00;
$color-warning-light: #ffa44c;
$color-warning-lighter: #ffd8b2;
$color-warning-lightest: #fff8f2;
/*成功色*/
$color-success: #00b42a;
$color-success-light: #4cca69;
$color-success-lighter: #b2e8bf;
$color-success-lightest: #f2fbf4;
/*黄色*/
$color-yellow: #ffbb00;
$color-yellow-light: #ffcf4c;
$color-yellow-lighter: #fff7e1;
$color-yellow-lightest: #fffcf5;
/*基础色*/
$color-white: #ffffff;
$color-black: #000000;
$color-gray: #e2e2e2;
$color-gray-nomal: #5f5f5f;
$color-gray-nomaler: #dddddd;
$color-gray-light: #eeeeee;
$color-gray-lighter: #f6f6f6;
$color-gray-lightest: #fafafa;
$color-gray-dark: #d2d2d2;
$color-gray-darker: #c2c2c2;
$color-gray-darkest: #333333;
$color-gray-master: #999999;
$color-gray-mastest: #666666;
/*中性色*/
$color-base: #2f363c;
$color-base-darker: #23292e;
$color-base-darkest: #1c1f23;

.mr30 {
  margin: 30rpx;
}
.pd30 {
  padding: 30rpx;
}
.pd030 {
  padding: 0 30rpx !important;
}
.pd03030 {
  padding: 0 30rpx 30rpx;
}
.navigator-hover {
  background: none;
}
.navigator-hover {
  background: transparent;
}
.inline {
  display: inline-block;
}
.hr {
  height: 20rpx !important;
  background: #f5f8fc !important;
  width: 100%;
}
.flex {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
}
.flext {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: flex-start;
  -webkit-align-items: flex-start;
  align-items: flex-start;
}
.flexb {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: flex-end;
  -webkit-box-align: flex-end;
  -webkit-align-items: flex-end;
}
.flex-bd {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  min-width: 0;
}
.bor-t,
.bor-b,
.bor-r,
.bor-l {
  position: relative;
}
.bor-t:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid rgb(214, 215, 217);
  color: rgb(214, 215, 217);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}

.bor-b:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  top: inherit;
  border-bottom: 1px solid rgb(214, 215, 217);
  color: rgb(214, 215, 217);
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.bor-r:before {
  content: " ";
  position: absolute;
  top: 0;
  right: 0;
  left: inherit;
  height: 100%;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.1);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleX(0.5);
  transform: scaleX(0.5);
}
.bor-l:before {
  content: " ";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  border-left: 1px solid rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.1);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleX(0.5);
  transform: scaleX(0.5);
}
image {
  vertical-align: middle;
}
input:disabled,
.uni-input-input:disabled {
  pointer-events: none;
}
.line1 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  word-wrap: break-word;
}
.line2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  word-wrap: break-word;
}
.tag0 {
  background: $color-success;
}
.tag1 {
  background: $color-main;
}
.tag2 {
  background: $color-warning;
}
.navbar {
  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
  z-index: 99;
}
.navbar-back {
  position: fixed;
  left: 20rpx;
  z-index: 100;
}
.navbar-back .icon {
  display: inline-block;
  width: 70rpx;
  height: 70rpx;
  line-height: 70rpx;
  vertical-align: middle;
  text-align: center;
  border-radius: 50%;
}
.navbar-back .icon i {
  color: #fff;
  font-size: 48rpx;
}
.navbar-back.black .icon {
  background: none;
}
.navbar-back.black .icon i {
  color: #333;
}
.navbar .content {
  position: fixed;
  text-align: center;
  color: #000;
  left: 0;
  width: 100%;
  font-size: 34rpx;
}

.user-top {
  padding: 30rpx;
  margin-bottom: 20rpx;
  background: #fff;
}
.user-top .flex-bd {
  margin-left: 20rpx;
}
.user-top .name {
  font-size: 32rpx;
}
.user-top .logtime {
  font-size: 24rpx;
  color: #999;
  margin-top: 15rpx;
}
.user-main {
  background: #fff;
  position: relative;
}
.cell {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  padding: 30rpx;
}
.cell-icon {
  margin-right: 20rpx;
}
.cell-content {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  min-width: 0;
  font-size: 28rpx;
}

.cell-icon image {
  width: 40rpx;
  height: 40rpx;
}

.cell-ft {
  font-size: 28rpx;
  color: #999;
}
.form-cell {
  padding: 24rpx 0;
}
.form-cell .cell-label {
  font-size: 30rpx;
  color: #333;
  width: 200rpx;
}
.form-cell .cell-value {
  flex: 1;
  text-align: right;
  font-size: 30rpx;
  color: #555;
  margin-left: 20rpx;
}

.scroll-content {
  position: relative;
}
.fixed-tab {
  width: 100%;
  top: 0;
  background: #fff;
  position: fixed;
  z-index: 2;
  height: 48px !important;
}
.fixed-tab-height {
  height: 48px !important;
}

.pd-list {
}
.pd-list .li {
  margin: 20rpx;
  background: #fff;
  border-radius: 4px;
}
.pd-list .li .name {
  position: relative;
  padding: 20rpx 0;
  margin: 0 30rpx;
}
.pd-list .li .name .flex-bd {
  font-size: 30rpx;
  font-weight: bold;
}

.pd-list .li .name .tag {
  font-size: 24rpx;
  padding: 3px 0;
  color: #fff;
  border-radius: 4px;
  min-width: 130rpx;
  text-align: center;
}
.check-tag-1 {
  background: $color-gray-darker;
}
.check-tag0 {
  background: $color-main;
}
.check-tag1 {
  background: $color-success;
}
.check-tag2 {
  background: $color-warning;
}
.pd-list .li .name .flex-hd {
  color: #999;
  font-size: 24rpx;
}
.pd-list .li .info {
  overflow: hidden;
  padding: 10rpx;
}
.pd-list .li .info .i {
  width: 50%;
  float: left;
  font-size: 28rpx;
  padding: 15rpx;
  box-sizing: border-box;
}
.pd-list .li .info .i.iw {
  width: 100%;
}
.pd-list .li .counts {
  overflow: hidden;
  padding: 10rpx 20rpx;
}
.pd-list .li .counts .i {
  width: 33.3%;
  float: left;
  font-size: 28rpx;
  padding: 15rpx;
  box-sizing: border-box;
}
.pd-list .li .counts .i .n {
  color: #608dfa;
  display: inline-block;
  padding-left: 10rpx;
}
.pd-list .li .span {
  display: inline-block;
  color: #999;
}
.pd-list .li .info .span {
  width: 130rpx;
}
.pd-list .li .btns {
  text-align: center;
  color: #608dfa;
  font-size: 30rpx;

  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
}
.pd-list .li .btns .btn {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  min-width: 0;
  margin: 15rpx 0;
  line-height: 50rpx;
  height: 50rpx;
  font-size: 28rpx;
}
.panD-list {
}
.panD-list .li {
  padding: 20rpx;
  background: #fff;
  margin: 20rpx;
  border-radius: 4px;
}
.panD-list .li .cover {
  width: 130rpx;
  height: 130rpx;
  border-radius: 4px;
  overflow: hidden;
}
.panD-list .li .img {
  margin-right: 20rpx;
  position: relative;
}
.panD-list .li .img .tag {
  font-size: 20rpx;
  position: absolute;
  bottom: 0;
  right: 0;
  color: #fff;
  padding: 0 2px;
}

.panD-list .li .name {
  font-size: 30rpx;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.panD-list .li .no {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}
.panD-list .li .tag-cate {
  font-size: 24rpx;
  border: 1px solid $color-main;
  color: $color-main;
  padding: 0px 5px;
  border-radius: 4px;
  display: inline-block;
  margin-right: 10rpx;
}

.bot-relative {
  position: relative;
  box-shadow: none;
  padding: 0 15rpx 60rpx 15rpx;
  box-sizing: border-box;
  height: 130rpx;
  line-height: 100rpx;
}

.pd-info {
  margin: 20rpx;
}
.pd-info .top {
  padding: 30rpx;
  border-radius: 4px;
  background: #fff;
}
.pd-info .top .name {
  font-size: 32rpx;
  margin-bottom: 10rpx;
  font-weight: bold;
}
.pd-info .top .no {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 10rpx;
}
.pd-info .top .cover {
  width: 120rpx;
  height: 120rpx;
  margin-left: 20rpx;
}
.pd-info .top .status {
  font-size: 26rpx;
  color: #999;
}
.pd-info .top .status-text {
  display: inline-block;
  padding: 0 10rpx;
  border-radius: 10rpx;
  color: #fff;
}
.pd-info .pannel {
  border-radius: 4px;
  background: #fff;
}
.pannel .tit {
  padding: 20rpx 0;
  margin: 0 30rpx;
  font-weight: bold;
  font-size: 30rpx;
}
.pannel .tit .tag {
  font-size: 24rpx;
  color: #fff;
  border-radius: 4px;
  padding: 2px 10rpx;
}
.pannel .tit .more {
  font-weight: normal;
  font-size: 24rpx;
  color: #999;
}
.fix-pannel {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  box-shadow: 1px 1px 12px rgba(0, 0, 0, 0.1);
}

.pannel .uv-upload__button {
  margin: 0 0 0 8px !important;
}
.pannel .uv-cell__body {
  padding-left: 0 !important ;
  padding-right: 0 !important;
}

.pannel.left .uv-radio-group--row {
  justify-content: flex-start;
}
.pannel.left .uv-upload__wrap {
  justify-content: flex-start;
}
.pannel.left .uv-upload__button {
  margin: 0 8px 0 0 !important;
  background: #fff !important;
}
.top-search {
  width: 100%;
  box-sizing: border-box;
  padding: 10px 20rpx;
  background: #fff;
}

.top-search .flex-hd {
  margin-left: 20rpx;
}
.top-search .flex-hd i {
  font-size: 42rpx;
}

.pop-select {
  width: 500rpx;
  background: #fff;
  overflow-y: scroll;
}
.pop-select .uv-form-item__body {
  padding: 10rpx 0 !important;
}
.pop-select .title {
  height: 44px;
  line-height: 44px;
  padding: 0 30rpx;
}
.pop-select .formItem {
  margin-bottom: 20rpx;
}
.pop-select .btns {
  overflow: hidden;
  width: 100%;
}
.pop-select .btns .btn {
  float: left;
  width: 30.6%;
  margin-right: 4%;
  background: #f6f6f6;
  text-align: center;
  height: 60rpx;
  line-height: 60rpx;
  border-radius: 4px;
  text-align: center;
  margin-top: 20rpx;
}
.pop-select .btns .btnon {
  background: #5883e6;
  color: #fff;
}
.pop-select .btns .btn:nth-child(3n) {
  margin-right: 0;
}

.his-line {
}
.his-line .li {
  padding-bottom: 30rpx;
}
.his-line .li .flex-hd {
  margin-right: 20rpx;
}
.his-line .li .dot {
  width: 11px;
  height: 11px;
  border-radius: 50%;
  background: $color-main;
  margin-top: 5px;
}
.his-line .li .line {
  width: 1px;
  height: 120%;
  background: #ddd;
  margin-left: 5px;
}
.his-line .li:last-child .line {
  display: none;
}
.his-line .li:last-child {
  padding-bottom: 0;
}
.his-line .li .time {
  font-size: 28rpx;
  color: #333;
}
.his-line .li .desc {
  color: #999;
  font-size: 26rpx;
  line-height: 1.6;
}
.his-line .li .name {
  font-size: 28rpx;
}
.his-line .li .status {
  font-size: 24rpx;
  color: $color-main;
}
.his-line .li .tops {
  margin-bottom: 10rpx;
}
.his-line .li .con {
  background: #f6f6f6;
  padding: 20rpx 30rpx;
  border-radius: 4px;
}
.fix-icon {
  position: fixed;
  bottom: 50rpx;
  right: 30rpx;
  width: 90rpx;
  line-height: 90rpx;
  height: 90rpx;
  background-image: linear-gradient(0deg, #608dfa, #5883e6);
  border-radius: 50%;
  text-align: center;
  color: #fff;
}
.fix-icon i {
  font-size: 48rpx;
}
._select .head {
  white-space: nowrap;
}
._select ._head {
  margin: 0 30rpx;
  background: #f6f6f6;
  padding: 10rpx 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  margin-top: 30rpx;
  border-radius: 4px;
}
._select ._head .i {
  display: inline-block;
}
._select ._head .i .icon {
  margin: 0 10rpx;
}
._select ._list {
  margin: 0 30rpx;
}
._select .list {
  height: 600rpx;
}
._select .list .i {
  padding: 30rpx 0;
}

._select .tit {
  padding: 30rpx;
  font-weight: bold;
  margin: 0;
  font-size: 32rpx;
}
.count-list {
  font-size: 28rpx;
  text-align: center;
}
.count-list .i .dot {
  width: 16rpx;
  height: 16rpx;
  margin-right: 20rpx;
  border-radius: 50%;
}
.count-list .i .name {
  font-size: 24rpx;
}
.count-list .i {
  display: inline-block;
  margin: 0 15rpx;
}
.count-list .i .count {
  font-size: 28rpx;
  margin-left: 10rpx;
}
.pannel .u-cell__value {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  min-width: 0;
}

.pannel .u-cell__body__content {
  flex: inherit !important;
}

.sp-eList {
  padding: 20rpx 0;
}
.sp-eList .li {
  padding: 20rpx;
  border-radius: 6px;
  margin: 12rpx 0;
  line-height: 1.6;
  background: #f7f7f7;
}
.sp-eList .li .t {
  font-size: 28rpx;
  font-weight: bold;
}
.sp-eList .li .lii {
  font-size: 26rpx;
  color: #666;
}
.sp-eList .li .lii .flex-bd {
  text-align: right;
}
.sp-eList .li .lii .flex-hd {
}

.scroll-content .head {
  white-space: nowrap;
}
.scroll-content ._head {
  background: #f6f6f6;
  padding: 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  position: fixed;
  background: #fff;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 99;
}
.scroll-content ._head .i {
  display: inline-block;
}
.scroll-content ._head .i .icon {
  margin: 0 10rpx;
}

.fixbtn .btns {
  height: 110rpx;
  width: 100%;
  position: fixed;
  bottom: 0;
  left: 0;
  background: #fff;
  padding-bottom: calc(constant(safe-area-inset-bottom));
  padding-bottom: calc(env(safe-area-inset-bottom)); /* 设置底部安全距离 */
  z-index: 9;
}
.fixbtn .blank {
  height: 120rpx;
  padding-bottom: calc(constant(safe-area-inset-bottom));
  padding-bottom: calc(env(safe-area-inset-bottom)); /* 设置底部安全距离 */
}
.fixbtn .btns .btn {
  width: 100%;
  font-size: 28rpx;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  background: $color-main;
  color: #fff;
  margin: 0 30rpx;
  border-radius: 4px;
  box-sizing: border-box;
}
.fixbtn .btns .btn.btn-bor {
  border: 1px solid $color-main;
  color: $color-main;
  background: $color-main-lightest;
}
.fixbtn .btns .red.btn-bor {
  border: 1px solid $color-danger;
  color: $color-danger;
  background: $uni-color-danger-lightest;
}
.fixbtn .btns .btn + .btn {
  margin-left: 0;
}
.fixbtn .btns .btn.bor {
  width: 180rpx;
  border: 1px solid #ddd;
  color: #333;
  background: transparent;
}
.bot-form {
}
.bot-form .form {
  position: fixed;
  bottom: 0;
  box-sizing: border-box;
  left: 0;
  width: 100%;
  box-shadow: 1px 1px 10px #9d9d9d;
  background: #fff;
  padding-bottom: calc(constant(safe-area-inset-bottom));
  padding-bottom: calc(env(safe-area-inset-bottom)); /* 设置底部安全距离 */
}
.bot-form .form .uv-form {
  padding: 0 40rpx 30rpx;
}
.bot-form .form .tit {
  padding: 30rpx 0;
  margin: 0 30rpx;
  font-size: 28rpx;
  font-weight: bold;
}
.bot-form .blank {
  height: 560rpx;
  padding-bottom: calc(constant(safe-area-inset-bottom));
  padding-bottom: calc(env(safe-area-inset-bottom)); /* 设置底部安全距离 */
}
.spannel .u-upload__wrap {
  justify-content: flex-start !important;
}

.u-page__item__slot-icon {
  width: 24px;
  height: 24px;
}

.fixed-add {
  position: fixed;
  bottom: 40px;
  right: 20px;
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  background: $color-main;
  border-radius: 50%;
}

.form-pannel {
  margin: 30rpx;
  background: #fff;
  border-radius: 8px;
  padding: 0 30rpx;
}

.uni-date-editor .icon-calendar {
  display: none !important;
}
.uni-date-editor .uni-date__x-input {
  padding-left: 0 !important;
  font-size: 15px !important;
  height: 24px !important;
  line-height: 24px !important;
  color: #c0c4cc !important;
}
.uni-date-editor .uni-date__x-input-active {
  color: #303133 !important;
}
.zi-card {
  padding: 30rpx 30rpx 0 30rpx !important;
}
.zi-card:last-child {
  padding-bottom: 30rpx !important;
}
.zi-card .uv-form {
  background: #f6f6f6;
  border-radius: 4px;
  padding: 0 30rpx;
}
.zi-card .u-form-item__body {
  padding: 15rpx 0 !important;
}
.zi-card .uni-input-placeholder,
.zi-card .uv-input,
.zi-card .uni-date__x-input {
  background: #f6f6f6 !important;
  font-size: 28rpx !important;
}
.zi-card .uv-upload__button {
  background: #fff !important;
  margin-left: 0 !important;
  margin-right: 10rpx;
}
.asset-img {
  width: 50rpx;
  height: 50rpx;
}
.u-steps-item__line {
  background: #ddd !important;
}
.safe-bot {
  width: 100%;
  height: 120rpx;
}
.common-filter-cover {
  position: fixed !important;
}
.list-add-btn{
	width: 90%;border: #1677ff 1px dotted;margin: 15rpx auto;border-radius: 10rpx;white-space: nowrap;text-align:center;color: #1677ff;line-height:70rpx;
	
}
.list-add-btn .u-icon{display: inline-block;}