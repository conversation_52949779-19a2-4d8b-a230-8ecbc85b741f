<template>
  <view>
    <uv-navbar title="资产统计" :fixed="true" :placeholder="true" :autoBack="false" :bgColor="$c.themeColor()" :titleStyle="{ color: '#fff' }">
      <template v-slot:left><view></view></template>
    </uv-navbar>
    <view class="pd-info">
      <view class="pannel">
        <view class="tit bor-b">按部门</view>
        <view class="pd30">
          <qiun-data-charts type="pie" :opts="opts" :chartData="chartData1" />
          <view class="count-list">
            <view class="i" v-for="(item, index) in list1" @click="detail(item)">
              <view class="dot inline" :style="'background:' + opts.color[index] + ';'"></view>
              <view class="name inline">{{ item.name }}</view>
              <view class="count inline">{{ item.value }}</view>
            </view>
          </view>
        </view>
      </view>
      <view class="hr"></view>
      <view class="pannel">
        <view class="tit bor-b">按分类</view>
        <view class="pd30">
          <qiun-data-charts type="pie" :opts="opts" :chartData="chartData2" />
          <view class="count-list">
            <view class="i" v-for="(item, index) in list2" @click="detail(item)">
              <view class="dot inline" :style="'background:' + opts.color[index] + ';'"></view>
              <view class="name inline">{{ item.name }}</view>
              <view class="count inline">{{ item.value }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="safe-bot"></view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      tabs: [
        {
          name: "按部门",
        },
        {
          name: "按分类",
        },
      ],
      chartData1: {},
      chartData2: {},
      list1: [],
      list2: [],
      //您可以通过修改 config-ucharts.js 文件中下标为 ['pie'] 的节点来配置全局默认参数，如都是默认参数，此处可以不传 opts 。实际应用过程中 opts 只需传入与全局默认参数中不一致的【某一个属性】即可实现同类型的图表显示不同的样式，达到页面简洁的需求。
      opts: {
        color: [
          "#1890FF",
          "#91CB74",
          "#FAC858",
          "#EE6666",
          "#73C0DE",
          "#3CA272",
          "#FC8452",
          "#9A60B4",
          "#37A2DA",
          "#ff9f7f",
          "#fb7293",
          "#E062AE",
          "#E690D1",
          "#e7bcf3",
          "#9d96f5",
          "#8378EA",
          "#96BFFF",
          "#1890FF",
          "#91CB74",
          "#FAC858",
          "#EE6666",
          "#73C0DE",
          "#3CA272",
          "#FC8452",
          "#9A60B4",
          "#37A2DA",
          "#ff9f7f",
          "#fb7293",
          "#E062AE",
          "#E690D1",
          "#e7bcf3",
          "#9d96f5",
          "#8378EA",
          "#96BFFF",
          "#1890FF",
          "#91CB74",
          "#FAC858",
          "#EE6666",
          "#73C0DE",
          "#3CA272",
          "#FC8452",
          "#9A60B4",
          "#37A2DA",
          "#ff9f7f",
          "#fb7293",
          "#E062AE",
          "#E690D1",
          "#e7bcf3",
          "#9d96f5",
          "#8378EA",
          "#96BFFF",
        ],
        padding: [5, 5, 5, 5],
        enableScroll: false,
        extra: {
          pie: {
            activeOpacity: 0.5,
            activeRadius: 10,
            offsetAngle: 0,
            labelWidth: 15,
            border: false,
            borderWidth: 3,
            borderColor: "#FFFFFF",
          },
        },
        legend: {
          show: false,
        },
      },
    };
  },
  onLoad() {
    this.$apis.getAssetStatisticsByDept({ ParentCode: "root" }).then((res) => {
      var list = [];
      res.data.forEach((item) => {
        list.push({
          name: item.OrgName,
          value: item.OrgNum,
        });
      });
      var data = {
        series: [
          {
            data: list,
          },
        ],
      };
      this.list1 = list;
      this.chartData1 = JSON.parse(JSON.stringify(data));
    });

    this.$apis.getAssetCateList({ ParentCode: "root" }).then((res) => {
      var promiseList = [];
      res.data.forEach((item) => {
        promiseList.push(this.$apis.getAssetStatisticsByCate({ ParentCode: item.Code }));
      });
      Promise.all(promiseList).then((res) => {
        console;
        var list = [];
        res.forEach((item) => {
          item.data.forEach((i) => {
            list.push({
              name: i.AssetCateName,
              value: i.AssetCateNum,
            });
          });
        });
        var data = {
          series: [
            {
              data: list,
            },
          ],
        };
        this.list2 = list;
        this.chartData2 = JSON.parse(JSON.stringify(data));
      });
    });
  },
  methods: {},
};
</script>

<style></style>
