<template>
  <view>
    <uv-navbar title="资产变更" :fixed="true" :placeholder="true" :autoBack="true" :bgColor="$c.themeColor()" leftIconColor="#ffffff" :titleStyle="{ color: '#fff' }"></uv-navbar>
    <view class="scroll-content">
      <view class="fixed-tab-height">
        <view class="fixed-tab" :style="'top: ' + divTop + 'px;'">
          <view class="top-search flex">
            <view class="flex-item">
              <uv-search shape="square" :height="28" :showAction="false" placeholder="请输入单据号" v-model="searchForm.BillNo" @search="search"></uv-search>
            </view>
            <view class="flex-hd" @click="popShow = true">
              <i class="iconfont icon-shaixuan"></i>
            </view>
          </view>
        </view>
      </view>
      <view class="scroll">
        <view class="pd-list">
          <view class="li" v-for="(item, index) in list" @click="$c.navigateTo('../assetsChange/detail?Code=' + item.Code)">
            <view class="name flex bor-b">
              <view class="flex-bd">
                {{ item.BillNo }}
              </view>
              <view :class="'tag check-tag' + item.WfStatus">{{ item.WfStatusCaption }}</view>
            </view>
            <view class="info">
              <view class="i flex">
                <view class="span">申请标题</view>
                <view class="flex-bd line1">{{ item.ApplyTitle }}</view>
              </view>
              <view class="i flex">
                <view class="span">经办人</view>
                <view class="flex-bd line1">{{ item.HandleUserName }}</view>
              </view>
              <view class="i flex">
                <view class="span">变更日期</view>
                <view class="flex-bd line1">{{ item.ApplyDateCaption }}</view>
              </view>
              <view class="i flex">
                <view class="span">经办单位</view>
                <view class="flex-bd line1">{{ item.OrgName }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <common-filter v-model="searchForm" :show="popShow" :filter-items="searchFilter" @close="popShow = false" @confirm="search" @reset="search" />

    <view class="pd30">
      <uv-load-more
        fontSize="12"
        color="#999"
        :status="loadmore.status"
        :loading-text="loadmore.loadingText"
        :loadmore-text="loadmore.loadmoreText"
        :nomore-text="loadmore.nomoreText" />
    </view>
    <uv-back-top
      :scroll-top="scrollTop"
      mode="square"
      :duration="0"
      :iconStyle="{
        fontSize: '32rpx',
        color: '#fff',
      }"
      :customStyle="'background-color:' + $c.themeColor()"></uv-back-top>
    <view class="fixed-add" @click="$c.navigateTo('../assetsChange/add?type=add')">
      <view class="inline"><uv-icon name="plus" :size="20" color="#fff"></uv-icon></view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      divTop: "",
      loadmore: {
        status: "loading",
        loadingText: "努力加载中",
        loadmoreText: "轻轻上拉",
        nomoreText: "没有更多了",
      },
      list: [],
      page: 1,
      scrollTop: 0,
      popShow: false,
      searchForm: {},
      searchFilter: [
        { name: "BillNo", type: "input", label: "单据号" },
        { name: "HandleUserName", type: "input", label: "经办人" },
        { name: "OrgName", type: "input", label: "经办单位" },
        // { name: "ApplyDate", value: "ApplyDateValue", type: "daterange", label: "变更日期" },
        {
          name: "WfInstanceStatusName",
          value: "WfInstanceStatus",
          type: "picker",
          pickerType: "status",
          label: "审核状态",
          columns: [
            { name: "待提交", value: -1 },
            { name: "待审核", value: 0 },
            { name: "已通过", value: 1 },
            { name: "未通过", value: 2 },
          ],
        },
      ],
    };
  },
  onLoad() {
    var sys = uni.getSystemInfoSync();
    console.log(sys);
    this.divTop = sys.statusBarHeight + 44;

    this.getList(1).then((res) => {
      this.list = res;
    });

    uni.$on("refreshList", () => {
      this.search();
    });
  },
  onUnload() {
    uni.$off("refreshList");
  },
  methods: {
    dateChange(e) {
      if (e[0] && e[1]) {
        this.searchForm.ApplyDate = e[0] + " ~ " + e[1];
      } else {
        this.searchForm.ApplyDate = "";
      }
    },
    statusConfirm(e) {
      this.searchForm.WfInstanceStatus = e.value[0].value;
      this.searchForm.WfInstanceStatusName = e.value[0].name;
      this.statusShow = false;
    },
    getList(page) {
      return new Promise((resolve) => {
        this.$apis
          .getAssetChangeList(
            {
              CreateUserCode: uni.getStorageSync("UserCode"), //登录人
              PageSize: 10,
              PageIndex: page,
              ...this.searchForm,
            },
            { custom: { loading: false } }
          )
          .then((res) => {
            if (res.data.length < 10) {
              this.loadmore.status = "nomore";
            }
            resolve(res.data);
          });
      });
    },
    search(e) {
      this.page = 1;
      this.loadmore.status = "loading";
      this.list = [];
      this.getList(1).then((res) => {
        this.list = res;
      });
      if (e) {
        uni.pageScrollTo({
          scrollTop: 0,
        });
      }
    },
  },

  onReachBottom() {
    if (this.loadmore.status == "nomore") {
      return;
    }
    var list = this.list;
    this.getList(this.page + 1).then((res) => {
      for (let index in res) {
        list.push(res[index]);
      }
      if (res.length > 0) {
        this.list = list;
        this.page++;
        if (res.length < 10) {
          this.loadmore.status = "nomore";
        }
      }
    });
  },
  onPageScroll(e) {
    this.scrollTop = e.scrollTop;
  },
};
</script>

<style></style>
