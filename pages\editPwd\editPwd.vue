<template>
  <view>
    <uv-navbar title="修改密码" :fixed="true" :placeholder="true" :autoBack="true" :bgColor="$c.themeColor()" leftIconColor="#ffffff" :titleStyle="{ color: '#fff' }"></uv-navbar>
    <view class="pd15">
      <uv-form labelWidth="90">
        <uv-form-item label="新密码" borderBottom><uv-input border="none" v-model="LoginPassword" type="password" placeholder="请输入新密码" /></uv-form-item>
        <uv-form-item label="重复密码"><uv-input border="none" v-model="ReLoginPassword" type="password" placeholder="请再次输入新密码" /></uv-form-item>
      </uv-form>
    </view>
    <view style="margin: 50rpx 30rpx">
      <uv-button :ripple="true" @click="submit" :hairline="false" :customStyle="{ backgroundColor: $c.themeColor(), borderColor: $c.themeColor() }" type="primary">确定</uv-button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      LoginPassword: "",
      ReLoginPassword: "",
    };
  },
  methods: {
    submit() {
      if (!this.LoginPassword) {
        return uni.$uv.toast("请输入新密码");
      }
      if (!this.ReLoginPassword) {
        return uni.$uv.toast("请再次输入新密码");
      }
      if (this.LoginPassword != this.ReLoginPassword) {
        return uni.$uv.toast("两次密码不一致");
      }
      this.$apis
        .changePassword({
          LoginUserCode: uni.getStorageSync("UserCode"),
          LoginPassword: this.LoginPassword,
        })
        .then((res) => {
          if (res.code == 101) {
            uni.showToast({
              icon: "success",
              title: "修改成功",
              duration: 1500,
            });
            setTimeout(function () {
              uni.removeStorageSync("UserInfo");
              uni.removeStorageSync("UserCode");
              uni.reLaunch({
                url: "../login/login",
              });
            }, 1500);
          } else {
            uni.showToast({
              icon: "error",
              title: res.msg,
              duration: 1500,
            });
          }
        });
    },
  },
};
</script>

<style>
page {
  background: #f6f6f6;
}
.pd15 {
  background: #fff;
  margin-top: 20rpx;
  padding: 0 30rpx;
}
</style>
