<template>
  <view>
    <uv-navbar title="资产盘点" :fixed="true" :placeholder="true" :autoBack="true" :bgColor="$c.themeColor()" leftIconColor="#ffffff" :titleStyle="{ color: '#fff' }"></uv-navbar>
    <view class="pd-info">
      <view class="top flext">
        <view class="flex-bd">
          <view class="name">{{ info.AssetName }}</view>
          <view class="no">
            编号:
            <view class="inline">{{ info.AssetNumber }}</view>
          </view>
        </view>
        <image class="cover" mode="aspectFill" @click="$c.viewImage(info.Smallimage)" v-if="info.Smallimage" :src="$c.getImageUrl(info.Smallimage)"></image>
        <image class="cover" v-else src="../../static/images/sbnopic.png"></image>
      </view>
      <view class="hr"></view>
      <view class="pannel">
        <view class="tit">资产信息</view>
        <view class="pd030">
          <uv-cell-group>
            <uv-cell title="资产分类" :value="info.AssetCateName"></uv-cell>
            <uv-cell title="规格型号" :value="info.Specification || '--'"></uv-cell>
            <uv-cell title="品牌" :value="info.BrandName || '--'"></uv-cell>
            <uv-cell title="单位" :value="info.Unit || '--'"></uv-cell>
            <uv-cell title="资产价值" :value="info.AssetPrice ? `¥${info.AssetPrice}` : '--'"></uv-cell>
            <uv-cell title="财务编号" :value="info.FinanceNumber || '--'"></uv-cell>
            <uv-cell title="来源" :value="info.SourceTypeCaption || '--'"></uv-cell>
            <uv-cell title="购置日期" :value="info.BuyDateCaption || '--'"></uv-cell>
            <uv-cell title="启用日期" :value="info.UseDateCaption || '--'"></uv-cell>
            <uv-cell title="使用期限" :value="info.ScrapMonth ? `${info.ScrapMonth}个月` : '--'" :border="checkStatus == 1"></uv-cell>
            <block v-if="checkStatus == 1">
              <uv-cell title="位置" :value="info.LocationNamePathCaption || '--'"></uv-cell>
              <uv-cell title="管理部门" :value="info.OrgName || '--'"></uv-cell>
              <uv-cell title="管理人" :value="info.UseDeptName || '--'"></uv-cell>
              <uv-cell title="使用部门" :value="info.ChargeUserName || '--'"></uv-cell>
              <uv-cell title="使用人" :value="info.UseUserName || '--'" :border="false"></uv-cell>
            </block>
          </uv-cell-group>
        </view>
      </view>
      <!-- 盘点单已完成 -->
      <block v-if="checkStatus == 1">
        <view class="hr"></view>
        <view class="pannel">
          <view class="tit">盘点信息</view>
          <view class="pd030">
            <uv-cell-group>
              <uv-cell title="盘点状态" :value="info.AssetCheckStatusCaption || '--'"></uv-cell>
              <uv-cell title="盘点人" :value="info.CheckUserName || '--'"></uv-cell>
              <uv-cell title="盘点时间" :value="info.CheckDateCaption || '--'" :border="false"></uv-cell>
            </uv-cell-group>
          </view>
        </view>
        <view class="hr"></view>
      </block>
      <!-- 盘点单未完成，且来源为点击详情或者扫码 -->
      <block v-if="(type == 'check' || type == 'detail') && checkStatus == 0">
        <view class="hr"></view>
        <view class="pannel">
          <view class="tit bor-b">盘点</view>
          <view class="pd030">
            <uv-form ref="uForm" labelWidth="85">
              <common-picker
                required
                label="盘点状态"
                pickerType="radio"
                v-model="form.AssetCheckStatus"
                :radioOptions="checkStatusList"
                v-model:displayValue="form.AssetCheckStatusName"></common-picker>
              <common-picker
                v-if="modifyFiled.includes('资产位置')"
                label="盘点位置"
                pickerType="location"
                v-model="form.LocationCode"
                v-model:displayValue="form.LocationName"></common-picker>
              <common-picker
                v-if="modifyFiled.includes('管理部门')"
                label="管理部门"
                pickerType="dept"
                v-model="form.ManageDeptCode"
                v-model:displayValue="form.ManageDeptName"></common-picker>
              <common-picker
                v-if="modifyFiled.includes('管理人')"
                label="管理人"
                pickerType="user"
                userType="dept"
                v-model="form.ManageUserCode"
                v-model:displayValue="form.ManageUserName"
                :condition="{ field: '管理部门', value: form.ManageDeptCode }"
                :watchValue="form.ManageDeptCode"></common-picker>
              <view v-if="modifyFiled.includes('使用部门')">
                <uv-form-item v-if="form.AssetStatus == 0" label="使用部门" borderBottom>
                  <uv-input border="none" v-model="form.UseDeptName" disabled disabledColor="#fff" type="text" />
                </uv-form-item>
                <common-picker
                  v-else
                  label="使用部门"
                  pickerType="dept"
                  v-model="form.UseDeptCode"
                  v-model:displayValue="form.UseDeptName"
                  :extra="{ LoginUserCode: form.LoginUserCode }"></common-picker>
              </view>
              <view v-if="modifyFiled.includes('使用人')">
                <uv-form-item v-if="form.AssetStatus == 0" label="使用人" borderBottom>
                  <uv-input border="none" v-model="form.UseUserName" disabled disabledColor="#fff" type="text" />
                </uv-form-item>
                <common-picker
                  v-else
                  label="使用人"
                  pickerType="user"
                  userType="dept"
                  v-model="form.UseUserCode"
                  v-model:displayValue="form.UseUserName"
                  :condition="{ field: '使用部门', value: form.UseDeptCode }"
                  :watchValue="form.UseDeptCode"></common-picker>
              </view>

              <uv-form-item label="图片" labelWidth="70">
                <common-upload v-model="form.ImageName" :maxCount="1"></common-upload>
              </uv-form-item>
            </uv-form>
          </view>
        </view>
      </block>
    </view>
    <!-- 盘点单未完成，且来源为点击详情或者扫码 -->
    <view class="fixbtn" v-if="(type == 'check' || type == 'detail') && checkStatus == 0">
      <view class="blank"></view>
      <view class="btns bor-t flex">
        <view class="btn" @click="submit()">确定</view>
      </view>
    </view>
    <!-- 来源为盘盈 -->
    <view class="fixbtn" v-if="type == 'profit' && checkStatus == 1">
      <view class="blank"></view>
      <view class="btns bor-t flex">
        <view class="btn btn-bor" @click="delProfit()">删除</view>
        <view class="btn" @click="$c.navigateTo('../assetsProfit/add?AssetCheckCode=' + code + '&detailCode=' + detailCode + '&type=edit')">修改</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      info: {},
      checkStatusList: [
        { name: "正常", value: 1 },
        { name: "盘亏", value: 2 },
      ], //盘点状态(1正常 2盘亏 3盘盈)
      form: {
        AssetCode: "", //资产编码
        AssetCheckCode: "", //盘点编码
        AssetStatus: "", //资产状态
        LoginUserCode: uni.getStorageSync("UserCode"),
        AssetCheckStatus: 1, //盘点状态(1正常 2盘亏 3盘盈)
        AssetCheckStatusName: "正常", // 添加显示值字段
        LocationCode: "", //资产位置
        LocationName: "", //资产位置
        UseUserCode: "", //使用人
        UseUserName: "", //使用人
        ManageUserCode: "", //管理人
        ManageUserName: "", //管理人
        UseDeptCode: "", //使用部门
        UseDeptName: "", //使用部门
        ManageDeptCode: "", //管理部门
        ManageDeptName: "", //管理部门
        ImageName: "", //图片
      },
      modifyFiled: "",
      type: "",
      code: "",
      detailCode: "",
      assetNumber: "",
      checkStatus: 0, //盘点单状态(0未结束 1已结束)
    };
  },
  onLoad(e) {
    if (e.modifyFiled) {
      this.modifyFiled = e.modifyFiled.split(",").filter(Boolean);
    }
    this.code = e.code;
    this.detailCode = e.detailCode;
    this.type = e.type;
    this.assetNumber = e.assetNumber;
    this.checkStatus = e.checkStatus;
    this.detail();

    //如果来源为check，则状态只有正常
    if (e.type == "check") {
      this.checkStatusList = [{ name: "正常", value: 1 }];
    }

    uni.$on("refreshProfitDetail", () => {
      this.detail();
    });
  },
  onUnload() {
    // 页面销毁时取消事件监听
    uni.$off("refreshProfitDetail");
  },
  methods: {
    detail() {
      this.$apis[this.type == "profit" ? "getAssetCheckProfitDetail" : "getAssetCheckDetailInfo"]({
        AssetNumber: this.assetNumber, //资产编码
        AssetCheckCode: this.code, //盘点编码
        Code: this.detailCode, //详情编码
      }).then((res) => {
        let info = res.data[0] || res.data;
        this.info = info;
        this.form.AssetCode = info.Code;
        this.form.AssetStatus = info.AssetStatus;
        this.form.AssetCheckCode = this.code;
        this.form.LocationCode = info.LocationCode;
        this.form.LocationName = info.LocationName;
        this.form.UseUserCode = info.UseUserCode;
        this.form.UseUserName = info.UseUserName;
        this.form.ManageUserCode = info.ChargeUserCode;
        this.form.ManageUserName = info.ChargeUserName;
        this.form.UseDeptCode = info.UseDeptCode;
        this.form.UseDeptName = info.UseDeptName;
        this.form.ManageDeptCode = info.OrgCode;
        this.form.ManageDeptName = info.OrgName;
        this.form.ImageName = info.Smallimage;
      });
    },
    delProfit() {
      uni.showModal({
        title: "提示",
        content: "确定删除吗？",
        success: (res) => {
          if (res.confirm) {
            this.$apis
              .deleteAssetCheckProfit({
                Code: this.detailCode,
                LoginUserCode: uni.getStorageSync("UserCode"),
              })
              .then((res) => {
                console.log(res);
                if (res.code == 100) {
                  uni.showToast({
                    icon: "success",
                    title: "删除成功",
                  });
                  setTimeout(() => {
                    uni.$emit("assetsCheckSuccess");
                    uni.navigateBack({
                      delta: 1,
                    });
                  }, 1500);
                } else {
                  uni.$uv.toast(res.msg);
                }
              });
          }
        },
      });
    },
    submit() {
      if (!this.form.AssetCheckStatus) {
        return uni.$uv.toast("请选择盘点结果");
      }
      this.$apis.submitAssetCheck(this.form).then((res) => {
        console.log(res);
        if (res.code == 100) {
          uni.showToast({
            icon: "success",
            title: "盘点成功",
          });
          setTimeout(() => {
            uni.$emit("assetsCheckSuccess");
            uni.navigateBack({
              delta: 1,
            });
          }, 1500);
        } else {
          uni.$uv.toast(res.msg);
        }
      });
    },
  },
};
</script>

<style></style>
