{"name": "PDAModule", "id": "dc-module", "version": "0.1", "description": "pda插件", "_dp_type": "nativeplugin", "_dp_nativeplugin": {"android": {"plugins": [{"type": "module", "name": "uniplugin_pdamodule", "class": "com.example.uniplugin_pdamodule.PDAModule"}, {"type": "module", "name": "uniplugin_scannermodule", "class": "io.aycloud.uniplugin.Scanner.ScannerModule"}, {"type": "module", "name": "uniplugin_QRCodeModule", "class": "io.aycloud.uniplugin.QRCode.QRCodeModule"}, {"type": "module", "name": "uniplugin_officemodule", "class": "io.aycloud.uniplugin.Office.OfficeModule"}, {"type": "module", "name": "uniplugin_rfid_module", "class": "com.example.uniplugin_rfid_module.TestModule"}], "dependencies": ["com.squareup.okhttp3:okhttp:4.10.0", "net.sourceforge.jexcelapi:jxl:2.6.12", "com.github.jenly1314.WeChatQRCode:opencv:2.0.1", "com.github.jenly1314.WeChatQRCode:opencv-armv7a:2.0.1", "com.github.jenly1314.WeChatQRCode:opencv-armv64:2.0.1", "com.github.jenly1314.WeChatQRCode:opencv-x86:2.0.1", "com.github.jenly1314.WeChatQRCode:opencv-x86_64:2.0.1", "com.github.jenly1314.WeChatQRCode:opencv-qrcode:2.0.1", "com.github.jenly1314.WeChatQRCode:opencv-qrcode-scanning:2.0.1", "com.github.jenly1314.WeChatQRCode:wechat-qrcode:2.0.1", "com.github.jenly1314.WeChatQRCode:wechat-qrcode-scanning:2.0.1", "com.github.jenly1314.MLKit:mlkit-camera-core:1.0.3", "com.github.jenly1314:zxing-lite:3.0.1", "com.alibaba:<PERSON><PERSON><PERSON>:1.2.83", "org.apache.poi:poi:5.2.2", "org.apache.poi:poi-ooxml:5.2.2"], "integrateType": "aar", "abis": ["armeabi|armeabi-v7a|arm64-v8a|mips|mips64|x86_64|x86"], "minSdkVersion": 26, "compileOptions": {"sourceCompatibility": "11", "targetCompatibility": "11"}, "permissions": ["android.permission.INTERNET", "android.permission.ACCESS_NETWORK_STATE"]}}}