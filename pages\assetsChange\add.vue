<template>
  <view>
    <uv-navbar title="资产变更" :fixed="true" :placeholder="true" :autoBack="true" :bgColor="$c.themeColor()" leftIconColor="#ffffff" :titleStyle="{ color: '#fff' }"></uv-navbar>
    <view class="pd-info">
      <view class="pannel">
        <view class="tit bor-b">单据信息</view>
        <view class="pd030">
          <uv-form labelWidth="90">
            <uv-form-item label="单据号" borderBottom required>
              <uv-input
                border="none"
                v-model="form.BillNo"
                :disabled="type == 'edit' || autoDjCode == 1"
                disabledColor="#fff"
                type="text"
                :placeholder="autoDjCode == 0 ? '请输入' : '自动生成'" />
            </uv-form-item>
            <uv-form-item required label="申请标题" borderBottom>
              <uv-input v-model="form.ApplyTitle" border="none" type="text" placeholder="请输入" />
            </uv-form-item>
            <uv-form-item required label="变更日期" borderBottom>
              <uni-datetime-picker type="date" v-model="form.ApplyDate" :border="false" :end="maxDate" :clear-icon="false"></uni-datetime-picker>
              <uv-icon slot="right" name="arrow-right"></uv-icon>
            </uv-form-item>
            <common-picker pickerType="user" label="经办人" v-model="form.HandleUserCode" v-model:displayValue="form.HandleUserName"></common-picker>
            <uv-form-item label="申请理由"><uv-input v-model="form.Remark" border="none" type="text" placeholder="请输入" /></uv-form-item>
          </uv-form>
        </view>
      </view>
      <view class="hr"></view>
      <view class="pannel">
        <view class="tit bor-b flex">
          <view class="flex-bd">资产信息</view>
        </view>
        <view class="pd030 zi-card" v-for="(item, index) in form.AssetChangeDetail" :key="index">
          <uv-form labelWidth="85" :labelStyle="{ fontSize: '28rpx' }">
            <block v-for="(i, idx) in asset">
              <uv-form-item v-if="i.type === 'image'" :label="i.desc">
                <image v-if="item[i.name]" :src="$c.getImageUrl(item[i.name])" mode="aspectFill" class="asset-img" @click="$c.viewImage(item[i.name])" />
                <text v-else>--</text>
              </uv-form-item>
              <uv-form-item v-if="i.type === 'input'" :label="i.desc" borderBottom :required="i.required">
                <uv-input v-model="item[i.name]" :disabled="i.disabled" border="none" :placeholder="i.disabled ? '--' : '请输入'" />
                <uv-button v-if="idx == 0" slot="right" type="info" customStyle="height:50rpx;font-size:24rpx;" @click="del(index)">删除</uv-button>
                <view v-if="i.ft" slot="right">{{ i.ft }}</view>
              </uv-form-item>
              <uv-form-item v-if="i.type === 'date'" :label="i.desc" borderBottom :required="i.required">
                <uni-datetime-picker type="date" v-model="item[i.name]" :border="false" :end="maxDate" :clear-icon="false"></uni-datetime-picker>
                <uv-icon slot="right" name="arrow-right"></uv-icon>
              </uv-form-item>
              <block v-if="i.type.includes('picker')">
                <common-picker
                  :required="i.required"
                  :label="i.desc"
                  :pickerType="i.pickerType"
                  :modelValue="item[i.value]"
                  :displayValue="item[i.name]"
                  @update:modelValue="(val) => updateValue(index, i.value, val)"
                  @update:displayValue="(val) => updateDisplayValue(index, i.name, val)"
                  :condition="i.condition ? { field: i.condition, value: item[i.watchValue] } : null"
                  :watchValue="i.watchValue ? item[i.watchValue] : null"
                  :extra="i.extra"></common-picker>
              </block>
              <uv-form-item v-if="i.type === 'upload'" :label="i.desc" :required="i.required">
                <common-upload v-model="item[i.name]" :maxCount="1"></common-upload>
              </uv-form-item>
            </block>
          </uv-form>
        </view>
        <view class="pd30" v-if="form.AssetChangeDetail.length == 0">
          <uv-empty :iconSize="70"></uv-empty>
        </view>
      </view>
    </view>
    <view class="fixed-add" @click="$c.selectAsset()" style="bottom: 200rpx">
      <view class="inline"><uv-icon name="plus" :size="20" color="#fff"></uv-icon></view>
    </view>
    <view class="fixbtn">
      <view class="blank"></view>
      <view class="btns bor-t flex">
        <view class="btn" @click="save()">保存</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      form: {
        BillNo: "",
        Code: "",
        HandleUserCode: uni.getStorageSync("UserCode"), //经办人
        HandleUserName: uni.getStorageSync("UserInfo").username, //经办人
        ApplyDate: "", //变更日期
        Remark: "", //备注
        ApplyTitle: "", //申请标题
        LoginUserCode: uni.getStorageSync("UserCode"), //登录人
        AssetChangeDetail: [],
      },
      type: "",
      maxDate: new Date().getTime(),
      asset: [
        { name: "AssetName", desc: "资产名称", type: "input", disabled: true },
        { name: "AssetCode", desc: "资产编码", type: "hidden" },
        { name: "AssetOrgCode", desc: "管理部门", type: "hidden" },
        { name: "OldUseOrgCode", desc: "原管理部门", type: "hidden" },
        { name: "OldUseDeptCode", desc: "原使用部门", type: "hidden" },
        { name: "OldUseUserCode", desc: "原使用人", type: "hidden" },
        { name: "OldLocationCode", desc: "原位置", type: "hidden" },
        { name: "UseOrgName", value: "UseOrgCode", desc: "管理部门", type: "picker", pickerType: "dept", required: true },
        {
          name: "UseDeptName",
          value: "UseDeptCode",
          desc: "使用部门",
          type: "picker",
          pickerType: "dept",
          extra: { LoginUserCode: uni.getStorageSync("UserCode") },
        },
        {
          name: "UseUserName",
          value: "UseUserCode",
          desc: "使用人",
          type: "picker",
          pickerType: "user",
          condition: "使用部门",
          watchValue: "UseDeptCode",
        },
        { name: "LocationName", value: "LocationCode", desc: "位置", type: "picker", pickerType: "location" },
        { name: "ChargeUserName", desc: "管理人", type: "input", disabled: true, condition: "管理部门", watchValue: "UseOrgCode" },
        { name: "BuyDate", desc: "购置日期", type: "input", disabled: true },
        { name: "UseDate", desc: "使用日期", type: "input", disabled: true },
        { name: "ScrapMonth", desc: "使用期限(月)", type: "input", disabled: true },
        { name: "Specification", desc: "规格型号", type: "input", disabled: true },
        { name: "Unit", desc: "计量单位", type: "input", disabled: true },
        { name: "SupplierName", desc: "供应商", type: "input", disabled: true },
        { name: "BrandName", desc: "品牌", type: "input", disabled: true },
        { name: "SourceTypeCaption", desc: "资产来源", type: "input", disabled: true },
        { name: "AssetStatusName", desc: "资产状态", type: "input", disabled: true },
        { name: "FinanceNumber", desc: "财政编号", type: "input", disabled: true },
        { name: "Smallimage", desc: "资产图片", type: "image", disabled: true },
      ],
      autoDjCode: 1, //单据号是否自动编号，默认1
    };
  },
  onLoad(e) {
    this.type = e.type;
    this.form.ApplyDate = uni.$uv.timeFormat(new Date().getTime(), "yyyy-mm-dd");
    this.form.AssetChangeDetail = [];

    //获取规则编码//获取规则编码
    this.autoDjCode = this.$c.getRuleCode("ZCBG");

    if (e.type == "edit") {
      this.$apis.getAssetChangeDetail({ Code: e.Code }).then((res) => {
        var form = {};
        for (let i in this.form) {
          if (res.data.AssetChange[i]) {
            this.form[i] = res.data.AssetChange[i];
          } else {
            console.log(i);
          }
        }
        this.form.Code = res.data.AssetChange.Code;

        var AssetChangeDetail = res.data.AssetChangeDetail;
        var arr = [];
        AssetChangeDetail.forEach((item) => {
          var newItem = {};
          this.asset.forEach((field) => {
            if (item[field.name] !== undefined) {
              newItem[field.name] = item[field.name];
            } else {
              newItem[field.name] = "";
              console.log(item.AssetName, field.name);
            }
            if (field.value) {
              if (item[field.value] !== undefined) {
                newItem[field.value] = item[field.value];
              } else {
                newItem[field.value] = "";
                console.log(item.AssetName, field.value);
              }
            }
            newItem.SourceTypeCaption = item.SourceTypeCaption;
            newItem.LocationName = item.LocationInfoName;
            newItem.AssetStatusName = item.AssetStatusNameCaption;
            newItem.UseOrgCode = item.AssetOrgCode;
            newItem.UseOrgName = item.AssetOrgName;
            newItem.BuyDate = item.BuyDateCaption;
            newItem.UseDate = item.UseDateCaption;
          });
          arr.push(newItem);
        });
        console.log(arr);
        this.form.AssetChangeDetail = arr;
      });
    }

    // 注册事件监听
    this.handleAssetSelect = (arr) => {
      console.log(arr);
      for (let i = 0; i < arr.length; i++) {
        this.form.AssetChangeDetail.push({
          AssetOrgCode: arr[i].OrgCode,
          AssetName: arr[i].AssetName,
          AssetCode: arr[i].Code,
          OldUseDeptCode: arr[i].UseDeptCode,
          OldUseOrgCode: arr[i].OrgCode,
          OldUseUserCode: arr[i].UseUserCode,
          OldLocationCode: arr[i].LocationCode,
          UseDeptName: arr[i].UseDeptName,
          UseOrgName: arr[i].OrgName,
          UseUserName: arr[i].UseUserName,
          LocationName: arr[i].LocationName,
          UseDeptCode: arr[i].UseDeptCode,
          UseOrgCode: arr[i].OrgCode,
          UseUserCode: arr[i].UseUserCode,
          LocationCode: arr[i].LocationCode,
          ChargeUserName: arr[i].ChargeUserName, //管理人
          BuyDate: arr[i].BuyDateCaption, //购置日期
          UseDate: arr[i].UseDateCaption, //使用日期
          ScrapMonth: arr[i].ScrapMonth, //使用期限(月)
          Specification: arr[i].Specification, //规格型号
          Unit: arr[i].Unit, //计量单位
          SupplierName: arr[i].SupplierName, //供应商
          BrandName: arr[i].BrandName, //品牌
          SourceTypeCaption: arr[i].SourceTypeCaption, //资产来源
          AssetStatusName: arr[i].AssetStatusCaption, //标签状态
          FinanceNumber: arr[i].FinanceNumber, //财政编号
          Smallimage: arr[i].Smallimage, //资产图片
        });
      }
    };

    uni.$on("assetSelect", this.handleAssetSelect);
  },
  onUnload() {
    uni.$off("assetSelect", this.handleAssetSelect);
  },
  methods: {
    updateValue(index, fieldName, value) {
      console.log(index, fieldName, value);
      this.form.AssetChangeDetail[index][fieldName] = value;
    },
    updateDisplayValue(index, fieldName, value) {
      console.log(index, fieldName, value);
      this.form.AssetChangeDetail[index][fieldName] = value;
    },
    del(index) {
      this.form.AssetChangeDetail.splice(index, 1);
    },
    save() {
      // 验证单据信息必填字段
      if (this.autoDjCode == 0 && !this.form.BillNo) {
        return uni.$uv.toast("请输入单据号");
      }
      if (!this.form.ApplyTitle) {
        return uni.$uv.toast("请输入申请标题");
      }
      if (!this.form.ApplyDate) {
        return uni.$uv.toast("请选择变更日期");
      }

      // 验证资产信息必填字段
      if (this.form.AssetChangeDetail.length === 0) {
        return uni.$uv.toast("请添加资产信息");
      }

      // 遍历验证每个资产的必填字段
      for (let i = 0; i < this.form.AssetChangeDetail.length; i++) {
        const item = this.form.AssetChangeDetail[i];

        // 遍历资产字段定义，检查必填项
        for (const field of this.asset) {
          if (field.required) {
            // 根据字段类型判断值的位置
            const value = field.value ? item[field.value] : item[field.name];
            if (value === "" || value === null || value === undefined) {
              return uni.$uv.toast(`第${i + 1}个资产的${field.desc}不能为空`);
            }
          }
        }
      }

      // 特殊处理下AssetOrgCode字段
      this.form.AssetChangeDetail.map((item) => {
        item.AssetOrgCode = item.UseOrgCode;
      });
      // 所有验证通过，调用保存接口
      this.$apis[this.type == "add" ? "addAssetChange" : "editAssetChange"](this.form).then((res) => {
        if (res.code === 100) {
          uni.$uv.toast("保存成功");
          setTimeout(() => {
            uni.navigateBack();
            if (this.type == "add") {
              uni.$emit("refreshList");
            } else {
              uni.$emit("refreshDetail");
            }
          }, 1500);
        } else {
          uni.$uv.toast(res.msg || "保存失败");
        }
      });
    },
  },
};
</script>

<style></style>
