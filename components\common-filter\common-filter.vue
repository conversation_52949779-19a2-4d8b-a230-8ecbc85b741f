<template>
  <view>
    <view :class="show ? 'common-filter-cover active' : 'common-filter-cover'"></view>
    <view :class="show ? 'common-filter active' : 'common-filter'">
      <view class="pop-select" :style="'margin-top: ' + (statusBarHeight + 44) + 'px;height:' + divHei + 'px'">
        <view class="title flex">
          <view class="flex-bd">筛选</view>
          <view class="flex-hd" @click="$emit('close')">
            <uv-icon name="close" :size="20"></uv-icon>
          </view>
        </view>

        <view class="pd030">
          <uv-form :labelStyle="{ fontSize: '28rpx', color: '#333' }" labelWidth="70">
            <view class="formItem" v-for="(item, index) in filterItems" :key="index">
              <!-- 普通输入框 -->
              <template v-if="item.type === 'input'">
                <uv-form-item :label="item.label" borderBottom>
                  <uv-input border="none" type="textarea" :placeholder="item.placeholder || '请输入'" :clearable="true" v-model="formData[item.name]" />
                </uv-form-item>
              </template>

              <!-- common-picker -->
              <template v-if="item.type === 'common-picker'">
                <common-picker
                  :label="item.label"
                  :pickerType="item.pickerType"
                  :modelValue="formData[item.value]"
                  :displayValue="formData[item.name]"
                  @update:modelValue="(val) => updateValue(item.value, val)"
                  @update:displayValue="(val) => updateDisplayValue(item.name, val)"></common-picker>
              </template>

              <!-- 日期范围选择 -->
              <template v-if="item.type === 'daterange'">
                <uv-form-item :label="item.label" borderBottom>
                  <uni-datetime-picker :border="false" :end="maxDate" v-model="formData[item.value]" type="daterange" @change="(e) => handleDateChange(e, item)" />
                </uv-form-item>
              </template>

              <!-- u-picker选择器 -->
              <template v-if="item.type === 'picker'">
                <uv-form-item :label="item.label" borderBottom @click="showPicker(item)">
                  <uv-input border="none" disabled disabledColor="#ffffff" type="text" placeholder="请选择" v-model="formData[item.name]" />
                  <uv-icon slot="right" name="arrow-right"></uv-icon>
                </uv-form-item>
              </template>
            </view>
          </uv-form>

          <view class="btn-group" style="padding: 50rpx 0">
            <uv-button
              :ripple="true"
              :hairline="false"
              :customStyle="{
                backgroundColor: themeColor,
                borderColor: themeColor,
                height: '80rpx',
              }"
              type="primary"
              @click="handleConfirm">
              查询
            </uv-button>
            <uv-button
              :ripple="true"
              :hairline="false"
              :customStyle="{
                marginTop: '20rpx',
                borderColor: themeColor,
                color: themeColor,
                height: '80rpx',
              }"
              type="info"
              :plain="true"
              @click="handleReset">
              重置
            </uv-button>
          </view>
        </view>
      </view>

      <!-- u-picker弹窗 -->
      <uv-picker ref="uvPicker" :columns="currentColumns" keyName="name" valueName="value" @confirm="handlePickerConfirm" @cancel="onPickerCancel"></uv-picker>
    </view>
  </view>
</template>

<script>
export default {
  name: "common-filter",

  props: {
    modelValue: {
      type: Object,
      default: () => ({}),
    },
    show: {
      type: Boolean,
      default: false,
    },
    filterItems: {
      type: Array,
      default: () => [],
    },
  },

  emits: ["update:modelValue", "close", "confirm", "reset"],

  data() {
    return {
      statusBarHeight: 0,
      divHei: 0,
      formData: {},
      currentColumns: [],
      currentPickerItem: null,
      maxDate: new Date().getTime(),
      themeColor: this.$c.themeColor(),
    };
  },

  watch: {
    modelValue: {
      handler(val) {
        console.log(val, "modelValue changed");
        const formData = {};
        this.filterItems.forEach((item) => {
          formData[item.name] = val.hasOwnProperty(item.name) ? val[item.name] : "";
          if (item.value) {
            formData[item.value] = val.hasOwnProperty(item.value) ? val[item.value] : "";
          }
        });
        this.formData = formData;
      },
      immediate: true,
      deep: true,
    },
  },

  created() {
    const sys = uni.getSystemInfoSync();
    this.statusBarHeight = sys.statusBarHeight;
    this.divHei = sys.windowHeight - sys.statusBarHeight - 44;
  },

  methods: {
    updateValue(fieldName, value) {
      this.formData[fieldName] = value;
    },
    updateDisplayValue(fieldName, value) {
      this.formData[fieldName] = value;
    },
    // 处理日期范围变化
    handleDateChange(e, item) {
      if (e[0] && e[1]) {
        this.formData[item.name] = e[0] + " - " + e[1];
        this.formData[item.value] = e;
      } else {
        this.formData[item.name] = "";
        this.formData[item.value] = "";
      }
    },

    // 显示picker
    showPicker(item) {
      console.log(item, "item");
      this.currentColumns = [item.columns];
      this.currentPickerItem = item;
      this.$refs.uvPicker?.open();
    },

    // picker确认
    handlePickerConfirm(e) {
      const item = this.currentPickerItem;
      this.formData[item.value] = e.value[0].value;
      this.formData[item.name] = e.value[0].name;
      // 更新父组件的值
      this.$emit("update:modelValue", { ...this.formData });
      this.$refs.uvPicker?.close();
    },

    onPickerCancel() {
      this.$refs.uvPicker?.close();
    },

    // 确认筛选
    handleConfirm() {
      this.$emit("update:modelValue", { ...this.formData });
      this.$emit("confirm", { ...this.formData });
      this.handleClose();
    },

    // 重置方法
    handleReset() {
      const formData = {};
      this.filterItems.forEach((item) => {
        // 如果type是hidden，保留原来的值
        if (item.type === "hidden") {
          formData[item.name] = this.formData[item.name];
          if (item.value) {
            formData[item.value] = this.formData[item.value];
          }
        } else {
          formData[item.name] = "";
          if (item.value) {
            formData[item.value] = "";
          }
        }
      });

      this.formData = formData;
      this.$emit("update:modelValue", { ...formData });
      this.$emit("reset", { ...formData });
      this.handleClose();
    },

    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss" scoped>
.btn-group {
  display: flex;
  flex-direction: column;

  .u-button {
    width: 100%;
    margin: 0;
  }
}
.common-filter {
  position: fixed;
  top: 0;
  right: -100vw;
  background: #ffffff;
  z-index: 10;
  transition: all 0.3s ease-in-out;
}
.common-filter.active {
  right: 0;
}
.common-filter-cover {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100%;
  z-index: 9;
  background: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: all 0.3s ease-in-out;
  pointer-events: none;
}
.common-filter-cover.active {
  opacity: 1;
}
</style>
