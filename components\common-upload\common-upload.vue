<template>
  <view class="my-upload">
    <uv-upload
      :width="width"
      :height="height"
      :maxCount="maxCount"
      :fileList="innerFileList"
      multiple
      @afterRead="handleAfterRead"
      @delete="handleDelete"
      :name="name"
      :capture="['album', 'camera']"
      :sourceType="['album', 'camera']"
      :previewFullImage="true"
      :compressed="true"
      @error="handleError"></uv-upload>
  </view>
</template>

<script>
export default {
  name: "common-upload",
  props: {
    // 图片宽度
    width: {
      type: [String, Number],
      default: 60,
    },
    // 图片高度
    height: {
      type: [String, Number],
      default: 60,
    },
    // 最大上传数量
    maxCount: {
      type: [String, Number],
      default: 10,
    },
    // 组件标识
    name: {
      type: [String, Number],
      default: "1",
    },
    // 双向绑定的值
    modelValue: {
      type: [String, Array],
      default: "",
    },
  },
  data() {
    return {
      innerFileList: [],
      isUploading: false,
      lastUploadTime: 0,
    };
  },
  watch: {
    modelValue: {
      handler(newVal) {
        const newFileList = newVal
          ? newVal.includes(",")
            ? newVal.split(",").map((pic) => ({
                url: this.$c.getImageUrl(pic),
                pic: pic,
              }))
            : [
                {
                  url: this.$c.getImageUrl(newVal),
                  pic: newVal,
                },
              ]
          : [];

        const currentPics = this.innerFileList.map((item) => item.pic).join(",");
        const newPics = newFileList.map((item) => item.pic).join(",");

        if (currentPics !== newPics) {
          this.innerFileList = newFileList;
        }
      },
      immediate: true,
    },
  },
  methods: {
    async handleAfterRead(event) {
      const lists = [].concat(event.file);
      const startIndex = this.innerFileList.length;

      // 先添加所有文件到列表中，状态为上传中
      lists.forEach((item, index) => {
        this.innerFileList.push({
          ...item,
          status: "uploading",
          message: "上传中",
        });
      });

      // 使用 Promise.all 并行处理所有上传
      try {
        await Promise.all(
          lists.map(async (item, index) => {
            const currentIndex = startIndex + index;
            try {
              const res = await this.$c.uploadFilePromise(item);
              // 使用 Vue.set 确保响应式更新
              this.$set(this.innerFileList, currentIndex, {
                status: "success",
                message: "",
                url: this.$c.getImageUrl(res.ServerRelativeFileName),
                pic: res.ServerRelativeFileName,
              });
            } catch (error) {
              // 使用 Vue.set 确保响应式更新
              this.$set(this.innerFileList, currentIndex, {
                ...item,
                status: "error",
                message: "上传失败",
              });
              console.error("上传失败:", error);
            }
          })
        );

        // 所有上传完成后，触发一次更新
        this.emitChange();
      } catch (error) {
        console.error("上传过程出错:", error);
      }
    },

    handleDelete(event) {
      this.innerFileList.splice(event.index, 1);
      this.emitChange();
    },

    emitChange() {
      const urls = this.innerFileList
        .filter((item) => item.status === "success" || !item.status)
        .map((item) => item.pic)
        .join(",");
      this.$emit("update:modelValue", urls);
      this.$emit("change", {
        files: JSON.parse(JSON.stringify(this.innerFileList)),
        urls: urls,
      });
    },

    // 获取当前文件列表
    getFileList() {
      return this.innerFileList;
    },
    // 获取拼接后的url字符串
    getUrls() {
      return this.innerFileList.map((item) => item.pic).join(",");
    },
    // 清空文件列表
    clear() {
      this.innerFileList = [];
    },
    // 处理上传错误
    handleError(error) {
      console.error("上传错误:", error);
      if (error.errMsg == "chooseImage:fail cancel") {
        uni.showToast({
          title: "取消上传",
          icon: "none",
        });
      } else {
        uni.showToast({
          title: "上传出错",
          icon: "none",
        });
      }
    },
  },
};
</script>

<style>
.my-upload {
  display: inline-block;
}
</style>
