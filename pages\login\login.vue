<template>
  <view>
    <view
      class="loginbg"
      :style="{
        backgroundImage: 'url(' + theme.backgroundImage + ')',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
      }">
      <view class="interface">
        <view class="logoimg">
          <uv-image v-if="theme.logo" :showLoading="true" :src="theme.logo" width="75%" height="auto" mode="widthFix"></uv-image>
        </view>
        <view class="title" v-if="theme.title">
          {{ theme.title }}
        </view>
        <view class="subtitle" v-if="theme.subTitle">
          {{ theme.subTitle }}
        </view>
      </view>
      <view class="login">
        <uv-form ref="uForm">
          <view class="form-item">
            <uv-form-item>
              <uv-input :fontSize="16" placeholder="请输入账号" v-model="LoginName" border="none">
                <template v-slot:prefix>
                  <i class="iconfont icon-yonghu"></i>
                </template>
              </uv-input>
            </uv-form-item>
          </view>
          <view class="form-item">
            <uv-form-item v-if="showPassword">
              <uv-input :fontSize="16" v-model="LoginPassword" placeholder="请输入密码" type="text" border="none">
                <template v-slot:prefix>
                  <i class="iconfont icon-mima"></i>
                </template>
                <template v-slot:suffix>
                  <view @click="showPassword = !showPassword">
                    <i class="iconfont icon-bukejian"></i>
                  </view>
                </template>
              </uv-input>
            </uv-form-item>
            <uv-form-item v-else>
              <uv-input :fontSize="16" v-model="LoginPassword" placeholder="请输入密码" type="password" border="none">
                <template v-slot:prefix>
                  <i class="iconfont icon-mima"></i>
                </template>
                <template v-slot:suffix>
                  <view @click="showPassword = !showPassword">
                    <i class="iconfont icon-kejian"></i>
                  </view>
                </template>
              </uv-input>
            </uv-form-item>
          </view>
          <view class="form-item-none">
            <uv-form-item>
              <view class="fullwidth">
                <view class="float-right inline-block">
                  <uv-switch v-model="isRemember" @change="changeRemember"></uv-switch>
                </view>
                <view class="inline-block">记住密码</view>
              </view>
            </uv-form-item>
          </view>
        </uv-form>
        <uv-button type="primary" :hairline="false" @click="login()" :customStyle="{ backgroundColor: $c.themeColor() }">登录</uv-button>
      </view>
    </view>
    <view class="copyright" v-if="copyright.isShow">
      <text v-if="copyright.companyName">版权所有 {{ copyright.companyName }}</text>
      <text v-if="copyright.phone">联系电话：{{ copyright.phone }}</text>
      <text v-if="copyright.verNo">版本号：{{ copyright.verNo }}</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      theme: {},
      copyright: {
        isShow: true,
        companyName: "江苏安友软件有限公司",
        //phone: "81185251",
        verNo: "v1.0.0",
      },
      isRemember: false,
      LoginName: "system",
      LoginPassword: "System@123456",
      showPassword: false,
    };
  },
  onLoad() {
    this.scan = this.$scan;
    this.theme = this.$theme;
    var isRemember = uni.getStorageSync("isRemember");
    if (isRemember === "") {
      this.isRemember = false;
    } else {
      this.isRemember = isRemember;
    }

    if (isRemember) {
      this.LoginPassword = uni.getStorageSync("LoginPassword");
    }
    if (uni.getStorageSync("LoginName")) {
      this.LoginName = uni.getStorageSync("LoginName");
    }

    if (uni.getStorageSync("UserCode")) {
      uni.reLaunch({
        url: "../index/index",
      });
    }

    //获取规则编码
    this.$apis.getRuleCodeList().then((res) => {
      var arr = [];
      for (let i in res.data) {
        arr.push({
          code: res.data[i].Code,
          isAuto: res.data[i].IsAuto,
        });
      }
      uni.setStorageSync("ruleCode", arr);
      console.log(arr);
    });
  },
  methods: {
    //是否记住密码
    changeRemember(e) {
      this.isRemember = e;
    },
    //登录
    login() {
      if (this.LoginName == "") {
        uni.showToast({
          icon: "error",
          title: "请输入账号",
          duration: 1500,
        });
        return;
      }
      if (this.LoginPassword == "") {
        uni.showToast({
          icon: "error",
          title: "请输入密码",
          duration: 1500,
        });
        return;
      }
      this.$apis
        .login({
          LoginName: this.LoginName,
          LoginPassword: this.LoginPassword,
        })
        .then((res) => {
          console.log(res);
          if (res.code == 100) {
            uni.showToast({
              icon: "success",
              title: "登录成功",
              duration: 1500,
            });
            uni.setStorageSync("UserInfo", res.data);
            uni.setStorageSync("UserCode", res.data.Code);
            setTimeout(function () {
              uni.reLaunch({
                url: "../index/index",
              });
            }, 1500);

            if (this.isRemember) {
              uni.setStorageSync("LoginName", this.LoginName);
              uni.setStorageSync("LoginPassword", this.LoginPassword);
              uni.setStorageSync("isRemember", true);
            } else {
              uni.setStorageSync("LoginName", "");
              uni.setStorageSync("LoginPassword", "");
              uni.setStorageSync("isRemember", false);
            }
          } else {
            uni.showToast({
              icon: "error",
              title: res.msg,
              duration: 1500,
            });
          }
        });
    },
    //离线盘点
    offlineInventory() {
      uni.navigateTo({
        url: "#",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.loginbg {
  min-height: 100vh;
  box-sizing: border-box;
  padding: 80rpx;
}

.interface {
  padding-top: 10vh;
}

.title {
  font-size: 50rpx;
  font-weight: 600;
  color: $uni-color-base-darkest;
  margin-top: 40rpx;
  line-height: 100rpx;
}

.subtitle {
  font-size: 30rpx;
  color: $uni-color-gray-mastest;
  margin-top: 100rpx;
}

.login {
  margin-top: 50rpx;
}

.login .form-item {
  padding: 0rpx 20rpx;
  border-radius: 10rpx;
  margin: 50rpx 0 20rpx 0;
  background: $uni-color-white;
}

.login .form-item-none {
  margin-bottom: 50rpx;
}

.copyright {
  position: absolute;
  bottom: 10rpx;
  width: 100%;
  text-align: center;
}

.copyright text {
  display: block;
  font-size: 24rpx;
  color: $uni-color-gray-mastest;
  margin: 10rpx 0;
}

.fullwidth {
  width: 100%;
}

.fullwidth .inline-block {
  width: auto;
  line-height: 50rpx;
}

.float-right {
  float: right;
  margin-right: 20rpx;
}
</style>
