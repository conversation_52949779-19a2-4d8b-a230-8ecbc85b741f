{"app": {"signingConfigs": [{"name": "default", "type": "HarmonyOS", "material": {"certpath": "C:\\Users\\<USER>\\.ohos\\config\\default_app-harmony-b82962ff_zRM8e0OFtKHNrHww7qO1o2A1ntRPIDDVI85MzqiVfjU=.cer", "keyAlias": "debugKey", "keyPassword": "0000001A7D8EC90F870326260B0150A18DC8FD0B3084D092D26E7F2EACFE609F006CE4D3E9D10C4D5DA6", "profile": "C:\\Users\\<USER>\\.ohos\\config\\default_app-harmony-b82962ff_zRM8e0OFtKHNrHww7qO1o2A1ntRPIDDVI85MzqiVfjU=.p7b", "signAlg": "SHA256withECDSA", "storeFile": "C:\\Users\\<USER>\\.ohos\\config\\default_app-harmony-b82962ff_zRM8e0OFtKHNrHww7qO1o2A1ntRPIDDVI85MzqiVfjU=.p12", "storePassword": "0000001AB9D902D474D531C1B6E2F0C94F154BAD8F1A40A4B419140526A0B31234D1A1DD8CD407AFB213"}}], "products": [{"name": "default", "signingConfig": "default", "compatibleSdkVersion": "5.0.0(12)", "compatibleSdkVersionStage": "beta6", "runtimeOS": "HarmonyOS", "buildOption": {"strictMode": {"caseSensitiveCheck": true, "useNormalizedOHMUrl": true}}}, {"name": "release", "signingConfig": "release", "compatibleSdkVersion": "5.0.0(12)", "runtimeOS": "HarmonyOS", "buildOption": {"strictMode": {"caseSensitiveCheck": true, "useNormalizedOHMUrl": true}}}], "buildModeSet": [{"name": "debug"}, {"name": "release"}]}, "modules": [{"name": "entry", "srcPath": "./entry", "targets": [{"name": "default", "applyToProducts": ["default", "release"]}]}]}