<template>
  <view>
    <uv-navbar title="资产入库" :fixed="true" :placeholder="true" :autoBack="true" :bgColor="$c.themeColor()" leftIconColor="#ffffff" :titleStyle="{ color: '#fff' }"></uv-navbar>
    <view class="pd-info">
      <view class="pannel">
        <view class="tit bor-b">单据信息</view>
        <view class="pd030">
          <uv-form labelWidth="90">
            <uv-form-item label="单据号" borderBottom required>
              <uv-input
                border="none"
                v-model="form.BillNo"
                :disabled="type == 'edit' || autoDjCode == 1"
                disabledColor="#fff"
                type="text"
                :placeholder="autoDjCode == 0 ? '请输入' : '自动生成'" />
            </uv-form-item>
            <uv-form-item required label="申请标题" borderBottom>
              <uv-input v-model="form.ApplyTitle" border="none" type="text" placeholder="请输入" />
            </uv-form-item>
            <uv-form-item required label="申请日期" borderBottom>
              <uni-datetime-picker type="date" v-model="form.ReceiptDateTime" :border="false" :end="maxDate" :clear-icon="false"></uni-datetime-picker>
              <uv-icon slot="right" name="arrow-right"></uv-icon>
            </uv-form-item>
            <common-picker pickerType="user" label="经办人" v-model="form.ReceiptUserCode" v-model:displayValue="form.ReceiptUserName"></common-picker>
            <uv-form-item label="申请理由"><uv-input v-model="form.Remark" border="none" type="text" placeholder="请输入" /></uv-form-item>
          </uv-form>
        </view>
      </view>
      <view class="hr"></view>
      <view class="pannel">
        <view class="tit bor-b flex">
          <view class="flex-bd">资产信息</view>
          <view class="flex-hd"></view>
        </view>
        <view class="pd030 zi-card" v-for="(item, index) in form.AssetReceiptDetail" :key="index">
          <uv-form labelWidth="80" :labelStyle="{ fontSize: '28rpx' }">
            <block v-for="(i, idx) in asset">
              <uv-form-item v-if="i.type === 'input'" :label="i.desc" borderBottom :required="i.required">
                <uv-input v-model="item[i.name]" border="none" :disabled="i.disabled" disabledColor="#fff" placeholder="请输入" />
                <view slot="right">
                  <uv-button v-if="idx == 0" type="info" customStyle="height:50rpx;font-size:24rpx;" @click="del(index)">删除</uv-button>
                  <view v-if="i.ft" slot="right">{{ i.ft }}</view>
                </view>
              </uv-form-item>
              <uv-form-item v-if="i.type === 'date'" :label="i.desc" borderBottom :required="i.required">
                <uni-datetime-picker type="date" v-model="item[i.name]" :border="false" :end="maxDate" :clear-icon="false"></uni-datetime-picker>
                <uv-icon slot="right" name="arrow-right"></uv-icon>
              </uv-form-item>
              <block v-if="i.type.includes('picker')">
                <common-picker
                  :required="i.required"
                  :label="i.desc"
                  :pickerType="i.pickerType"
                  :modelValue="item[i.value]"
                  :displayValue="item[i.name]"
                  @update:modelValue="(val) => updateValue(index, i.value, val)"
                  @update:displayValue="(val) => updateDisplayValue(index, i.name, val)"></common-picker>
              </block>
              <uv-form-item v-if="i.type === 'upload'" :label="i.desc" :required="i.required">
                <common-upload v-model="item[i.name]" :maxCount="1" :key="`upload-${index}`"></common-upload>
              </uv-form-item>
            </block>
          </uv-form>
        </view>
        <view class="pd30" v-if="form.AssetReceiptDetail.length == 0">
          <uv-empty :iconSize="70"></uv-empty>
        </view>
      </view>
    </view>
    <view class="fixed-add" @click="add()" style="bottom: 200rpx">
      <view class="inline"><uv-icon name="plus" :size="20" color="#fff"></uv-icon></view>
    </view>
    <view class="fixbtn">
      <view class="blank"></view>
      <view class="btns bor-t flex">
        <view class="btn" @click="save()">保存</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      form: {
        BillNo: "",
        Code: "",
        ReceiptUserCode: uni.getStorageSync("UserCode"), //经办人
        ReceiptUserName: uni.getStorageSync("UserInfo").username, //经办人
        ReceiptDateTime: "", //入库日期
        Remark: "", //备注
        ApplyTitle: "", //申请标题
        LoginUserCode: uni.getStorageSync("UserCode"), //登录人
        AssetReceiptDetail: [],
      },
      type: "",
      maxDate: new Date().getTime(),
      asset: [
        { name: "AssetName", desc: "资产名称", type: "input", required: true },
        { name: "CateName", value: "AssetCateCode", desc: "资产分类", type: "picker", pickerType: "category", required: true },
        { name: "OrgName", value: "OrgCode", desc: "管理部门", type: "picker", pickerType: "dept", required: true },
        { name: "Amount", desc: "数量", type: "input", inputType: "number", required: true },
        { name: "BuyDate", desc: "购置日期", type: "date", required: true },
        { name: "SourceTypeCaption", value: "SourceType", desc: "来源", type: "picker", pickerType: "source", required: true },
        { name: "SupplierName", value: "SupplierCode", desc: "供应商", type: "picker", pickerType: "supplier" },
        { name: "BrandName", value: "BrandCode", desc: "品牌", type: "picker", pickerType: "brand" },
        { name: "WarehouseName", value: "WarehouseCode", desc: "仓库", type: "picker", pickerType: "warehouse" },
        { name: "Specifications", desc: "规格型号", type: "input" },
        { name: "Unit", desc: "单位", type: "input" },
        { name: "ScrapMonth", desc: "使用期限", type: "input", ft: "月", default: "0" },
        { name: "AssetPrice", desc: "资产价值", type: "input", inputType: "number", default: "0" },
        { name: "LocationName", value: "LocationCode", desc: "位置", type: "picker", pickerType: "location" },
        { name: "Remark", desc: "备注", type: "input" },
        { name: "Smallimage", desc: "缩略图", type: "upload" },
      ],
      autoBhCode: 1, //资产编号是否自动编号，默认1
      autoDjCode: 1, //单据号是否自动编号，默认1
    };
  },
  onLoad(e) {
    this.type = e.type;
    this.form.ReceiptDateTime = uni.$uv.timeFormat(new Date().getTime(), "yyyy-mm-dd");
    this.form.AssetReceiptDetail = [];

    //获取规则编码
    this.autoDjCode = this.$c.getRuleCode("ZCRK");
    this.autoBhCode = this.$c.getRuleCode("ZCBH");
    if (this.autoBhCode == 0) {
      this.asset.forEach((field) => {
        if (field.name == "Amount") {
          field.default = "1";
          field.disabled = true;
        }
      });
      this.asset.unshift({ name: "AssetNumber", value: "AssetNumber", desc: "资产编号", type: "input", required: true, disabled: this.type == "edit" });
    }

    if (e.type == "edit") {
      this.$apis.getAssetReceiptDetail({ Code: e.Code }).then((res) => {
        var form = {};
        for (let i in this.form) {
          if (res.data.AssetReceipt[i]) {
            this.form[i] = res.data.AssetReceipt[i];
          } else {
            console.log(i);
          }
        }
        this.form.Code = res.data.AssetReceipt.Code;

        var AssetReceiptDetail = res.data.AssetReceiptDetail;
        var arr = [];
        AssetReceiptDetail.forEach((item) => {
          var newItem = {};
          this.asset.forEach((field) => {
            if (item[field.name] !== undefined) {
              newItem[field.name] = item[field.name];
            } else {
              newItem[field.name] = "";
              console.log(item.AssetName, field.name);
            }
            if (field.value) {
              if (item[field.value] !== undefined) {
                newItem[field.value] = item[field.value];
              } else {
                newItem[field.value] = "";
                console.log(item.AssetName, field.value);
              }
            }
            newItem.LocationName = item.LocationInfoName;
            newItem.BuyDate = item.BuyDateCaption;
          });
          arr.push(newItem);
        });
        console.log(arr);
        this.form.AssetReceiptDetail = arr;
      });
    }

    uni.$on("assetPurchaseSelect", (arr) => {
      arr.forEach((item) => {
        this.form.AssetReceiptDetail.push({
          AssetName: item.AssetName,
          Amount: item.ReceiptQuantityCaption,
          BuyDate: item.BuyDateCaption,
          SupplierName: item.SupplierName,
          BrandName: item.BrandName,
          Specifications: item.Specification,
          Unit: item.Unit,
          AssetPrice: item.Price,
          Smallimage: item.Smallimage,
        });
      });
    });
  },
  methods: {
    updateValue(index, fieldName, value) {
      console.log(index, fieldName, value);
      this.form.AssetReceiptDetail[index][fieldName] = value;
    },
    updateDisplayValue(index, fieldName, value) {
      console.log(index, fieldName, value);
      this.form.AssetReceiptDetail[index][fieldName] = value;
    },
    add() {
      const newAsset = {};
      console.log(this.asset);
      this.asset.forEach((field) => {
        newAsset[field.name] = field.type === "upload" ? "" : field.default || "";
        if (field.value) {
          newAsset[field.value] = "";
        }
      });
      this.$set(this.form.AssetReceiptDetail, this.form.AssetReceiptDetail.length, JSON.parse(JSON.stringify(newAsset)));
    },
    del(index) {
      if (this.form.AssetReceiptDetail[index]) {
        this.form.AssetReceiptDetail[index].Smallimage = "";
      }
      this.form.AssetReceiptDetail.splice(index, 1);
    },
    save() {
      if (this.autoDjCode == 0 && !this.form.BillNo) {
        return uni.$uv.toast("请输入单据号");
      }

      if (!this.form.ApplyTitle) {
        return uni.$uv.toast("请输入申请标题");
      }
      if (!this.form.ReceiptDateTime) {
        return uni.$uv.toast("请选择申请日期");
      }

      // 验证资产信息必填字段
      if (this.form.AssetReceiptDetail.length === 0) {
        return uni.$uv.toast("请添加资产信息");
      }

      // 遍历验证每个资产的必填字段
      for (let i = 0; i < this.form.AssetReceiptDetail.length; i++) {
        const item = this.form.AssetReceiptDetail[i];

        // 遍历资产字段定义，检查必填项
        for (const field of this.asset) {
          if (field.required) {
            // 根据字段类型判断值的位置
            const value = field.value ? item[field.value] : item[field.name];

            // 修改验证逻辑，正确处理0值的情况
            if (value === "" || value === null || value === undefined) {
              return uni.$uv.toast(`第${i + 1}个资产的${field.desc}不能为空`);
            }
          }
        }
      }

      // 所有验证通过，调用保存接口
      this.$apis[this.type == "add" ? "addAssetReceipt" : "editAssetReceipt"](this.form).then((res) => {
        if (res.code === 100) {
          uni.$uv.toast("保存成功");
          setTimeout(() => {
            uni.navigateBack();
            if (this.type == "add") {
              uni.$emit("refreshList");
            } else {
              uni.$emit("refreshDetail");
            }
          }, 1500);
        } else {
          uni.$uv.toast(res.msg || "保存失败");
        }
      });
    },
  },
};
</script>

<style></style>
