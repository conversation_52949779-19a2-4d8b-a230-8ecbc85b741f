import http from "@/common/http/index.js";
var apis = {};
//用户
//登录
apis.login = (form, config) => api("/Login/Check", "", form, config);
//首页统计
apis.getHomeCount = (form, config) => api("/AssetStatistics/GetUserByNum", "", form, config);
//修改密码
apis.changePassword = (form, config) => api("/Login/Execute", "password", form, config);

//资产
//获取资产列表
apis.getAssetsList = (form, config) => api("/Asset/GetPageList", "", form, config);
//资产分类
apis.getAssetCateList = (form, config) => api("/AssetCate/GetPageList", "", form, config);
//资产详情
apis.getAssetDetail = (form, config) => api("/Asset/GetEntity", "", form, config);
//资产新增
apis.addAsset = (form, config) => api("/Asset/Execute", "add", form, config);
//资产修改
apis.editAsset = (form, config) => api("/Asset/Execute", "modify", form, config);
//选择仓库
apis.getWarehouseList = (form, config) => api("/Warehouse/GetPageList", "", form, config);
//选择单位部门
apis.getDeptList = (form, config) => api("/FamsApp/GetPageListByOrg", "", form, config);
//选择供应商
apis.getSupplierList = (form, config) => api("/Supplier/GetPageList", "", form, config);
//选择品牌
apis.getBrandList = (form, config) => api("/Brand/GetPageList", "", form, config);
//选择人员
apis.getUserList = (form, config) => api("/FamsApp/GetPageListByUser", "", form, config);
//选择位置
apis.getLocationList = (form, config) => api("/LocationInfo/GetPageList", "", form, config);
//获取扩展属性
apis.getExtendPropertyList = (form, config) => api("/Asset/GetListByExtAttr", "", form, config);
//获取资产履历
apis.getAssetHistoryList = (form, config) => api("/Asset/GetListByHistory", "", form, config);

//资产入库
//列表
apis.getAssetReceiptList = (form, config) => api("/AssetReceipt/GetPageList", "", form, config);
//新增
apis.addAssetReceipt = (form, config) => api("/AssetReceipt/Execute", "add", form, config);
//修改
apis.editAssetReceipt = (form, config) => api("/AssetReceipt/Execute", "modify", form, config);
//详情
apis.getAssetReceiptDetail = (form, config) => api("/AssetReceipt/GetEntity", "", form, config);
//提交
apis.submitAssetReceipt = (form, config) => api("/AssetReceipt/Execute", "submit", form, config);
//审批
apis.approveAssetReceipt = (form, config) => api("/AssetReceipt/Execute", "approve", form, config);
//删除
apis.delAssetReceipt = (form, config) => api("/AssetReceipt/Execute", "delete", form, config);

//资产变更
//列表
apis.getAssetChangeList = (form, config) => api("/AssetChange/GetPageList", "", form, config);
//新增
apis.addAssetChange = (form, config) => api("/AssetChange/Execute", "add", form, config);
//修改
apis.editAssetChange = (form, config) => api("/AssetChange/Execute", "modify", form, config);
//详情
apis.getAssetChangeDetail = (form, config) => api("/AssetChange/GetEntity", "", form, config);
//提交
apis.submitAssetChange = (form, config) => api("/AssetChange/Execute", "submit", form, config);
//审批
apis.approveAssetChange = (form, config) => api("/AssetChange/Execute", "approve", form, config);
//删除
apis.delAssetChange = (form, config) => api("/AssetChange/Execute", "delete", form, config);

//资产盘点
//列表
apis.getAssetCheckList = (form, config) => api("/AssetCheck/GetPageList", "", form, config);
//详情
apis.getAssetCheckDetail = (form, config) => api("/AssetCheck/GetEntity", "", form, config);
//详情从表
apis.getAssetCheckDetailList = (form, config) => api("/AssetCheck/GetDetailList", "", form, config);
//盘点信息
apis.getAssetCheckDetailInfo = (form, config) => api("/AssetCheck/GetDetailEntity", "", form, config);
//提交盘点
apis.submitAssetCheck = (form, config) => api("/AssetCheck/Execute", "check", form, config);
//提交rfid盘点
apis.submitAssetCheckRfid = (form, config) => api("/AssetCheck/Execute", "rfidcheck", form, config);
//结束盘点
apis.endAssetCheck = (form, config) => api("/AssetCheck/Execute", "endcheck", form, config);
//审批
apis.approveAssetCheck = (form, config) => api("/AssetCheck/Execute", "approve", form, config);

//盘盈
//列表
apis.getAssetCheckProfitList = (form, config) => api("/AssetCheck/GetPageListByProfit", "", form, config);
//新增
apis.addAssetCheckProfit = (form, config) => api("/AssetCheck/ExecuteByProfit", "add", form, config);
//修改
apis.editAssetCheckProfit = (form, config) => api("/AssetCheck/ExecuteByProfit", "modify", form, config);
//删除
apis.deleteAssetCheckProfit = (form, config) => api("/AssetCheck/ExecuteByProfit", "delete", form, config);
//详情
apis.getAssetCheckProfitDetail = (form, config) => api("/AssetCheck/GetEntityByProfit", "", form, config);

//数据字典
apis.getDicList = (form, config) => api("/AssetReceipt/GetDataDic", "", form, config);
//规则编码
apis.getRuleCodeList = (form, config) => api("/NumberRules/GetPageList", "", form, config);

//资产统计
//根据部门
apis.getAssetStatisticsByDept = (form, config) => api("/AssetStatistics/GetPageListByOrg", "", form, config);
//根据分类
apis.getAssetStatisticsByCate = (form, config) => api("/AssetStatistics/GetPageListByCate", "", form, config);


//审核
//审核列表
apis.getAuditList = (form, config) => api("/Wf/GetWfTaskPageList", "", form, config);
//审核历史数据
apis.getAuditHistoryList = (form, config) => api("/Wf/GetDealList", "", form, config);
//当前审核进度
apis.getAuditProgress = (form, config) => api("/Wf/GetWFApprovalProgress", "", form, config);
function api(api, docmd, form, config) {
    return new Promise((resolve, reject) => {
        http
            .post(
                api,
                {
                    DoCmd: docmd,
                    ...form,
                },
                { custom: { ...config } }
            )
            .then((res) => {
                resolve(res);
            })
            .catch((res) => {
                reject(res);
            });
    });
}

export default apis;
