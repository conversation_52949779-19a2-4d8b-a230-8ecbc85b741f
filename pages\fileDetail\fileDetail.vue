<template>
  <view>
    <uv-navbar title="资产盘点" :fixed="true" :placeholder="true" :autoBack="true" :bgColor="$c.themeColor()" leftIconColor="#ffffff" :titleStyle="{ color: '#fff' }"></uv-navbar>
    <view class="file-info">
      <view class="tit">{{ text }}</view>
      <view class="count">
        <view class="i">
          <view class="num">{{ notCheckCount + checkCount }}</view>
          <view class="t">
            <view class="dot dot1"></view>
            总数
          </view>
        </view>
        <view class="i">
          <view class="num">{{ checkCount }}</view>
          <view class="t">
            <view class="dot dot2"></view>
            已盘点
          </view>
        </view>
        <view class="i">
          <view class="num">{{ notCheckCount }}</view>
          <view class="t">
            <view class="dot dot3"></view>
            未盘点
          </view>
        </view>
      </view>
    </view>
    <uv-tabs :list="tabs" @click="click" :current="current" :scrollable="false" :itemStyle="{ height: '38px' }" v-if="status == 1"></uv-tabs>
    <view class="panD-list" v-if="status == 1">
      <view class="li flex" @click="goDetail(item)" v-for="(item, index) in list">
        <view class="flex-bd">
          <view class="name">{{ item.name }}</view>
          <view class="no">资产编码:{{ item.code }}</view>
          <!-- <view class="tags">
            <view class="tag-cate">
              {{ item.AssetCateName }}
            </view>
          </view> -->
        </view>
        <view class="cell-ft">
          <uv-icon name="arrow-right" color="#999" :size="16"></uv-icon>
        </view>
      </view>
    </view>
    <view class="fixbtn">
      <view class="blank"></view>
      <view class="btns bor-t flex" v-if="status == 0">
        <view class="btn" @click="submit()">结束盘点</view>
      </view>
      <view class="btns bor-t flex" v-else>
        <view class="btn" style="background: #ddd; color: #999">已盘点</view>
      </view>
    </view>
  </view>
</template>

<script>
// #ifdef APP-PLUS
var pdamodule = uni.requireNativePlugin("uniplugin_pdamodule");
var officemodule = uni.requireNativePlugin("uniplugin_officemodule");
var testModule = uni.requireNativePlugin("uniplugin_rfid_module");
// #endif
export default {
  data() {
    return {
      tabs: [{ name: "已盘" }, { name: "未盘" }],
      current: 0,
      list: [],
      notCheckCount: 0,
      checkCount: 0,
      title: "",
      path: "",
      status: 0,
      checkStatus: 1,
    };
  },
  onLoad(e) {
    var path = decodeURIComponent(e.path);
    this.text = e.text;
    this.path = path;
    officemodule.getExcelData(
      {
        filePath: path,
      },
      (res) => {
        if (res.status == "success") {
          this.notCheckCount = res.data.notCheckCount;
          this.checkCount = res.data.checkCount;
          this.status = res.extdata;

          if (res.extdata == 1) {
            this.getList();
          }

          if (res.extdata == 0) {
            // #ifdef APP
            console.log(this.$c.isPda1());
            if (this.$c.isPda1()) {
              pdamodule.ClearCheckBillCode({}, (res) => {
                console.log(res);
                pdamodule.onRfidBack({}, (res) => {
                  console.log(res);
                  var RFIDCode = JSON.parse(res.data)[0].obj;
                  var data = [];
                  for (let i in RFIDCode) {
                    data.push(RFIDCode[i]);
                  }
                  console.log({
                    filePath: this.path,
                    numbers: RFIDCode,
                  });
                  //提交rfid盘点
                  officemodule.rfidCheck(
                    {
                      filePath: this.path,
                      numbers: RFIDCode,
                    },
                    (res) => {
                      if ((res.status = "success")) {
                        this.notCheckCount = res.data.notCheckCount;
                        this.checkCount = res.data.checkCount;
                      }
                    }
                  );
                });
              });
            }
            // #endif
          }
        }
        console.log(res);
      }
    );
  },
  onHide() {
    // #ifdef APP
    if (this.$c.isPda1()) {
      pdamodule.StopBack({}, (res) => {});
    }
    // #endif
  },
  mounted() {},
  methods: {
    click(e) {
      console.log(e);
      if (e.name == "已盘") {
        this.checkStatus = 1;
      } else {
        this.checkStatus = 0;
      }
      this.getList();
    },
    getList() {
      officemodule.getCheckDetailList(
        {
          filePath: this.path,
          checkStatus: this.checkStatus,
        },
        (res) => {
          if (res.status == "success") {
            for (let i in res.data) {
              res.data[i].name = res.data[i]["资产名称"];
              res.data[i].code = res.data[i]["资产编码"];
            }
            this.list = res.data;
          }
        }
      );
    },
    submit() {
      uni.showModal({
        title: "提示",
        content: "确定结束盘点吗？",
        success: (res) => {
          if (res.confirm) {
            // #ifdef APP-PLUS
            officemodule.endCheck(
              {
                filePath: this.path,
              },
              (res) => {
                console.log(res);
              }
            );
            // #endif
            uni.navigateBack();
          } else if (res.cancel) {
            console.log("用户点击取消");
          }
        },
      });
    },
  },
};
</script>

<style>
.file-info {
  padding: 30rpx;
}
.file-info .tit {
  margin-bottom: 20rpx;
  font-size: 28rpx;
}
.file-info .count {
  background: #fff;
  display: flex;
  padding: 30rpx 0;
}
.file-info .count .i {
  flex: 1;
  text-align: center;
}
.file-info .count .num {
  font-size: 36rpx;
}
.file-info .count .t {
  font-size: 24rpx;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10rpx;
}
.file-info .count .dot {
  width: 10rpx;
  height: 10rpx;
  border-radius: 50%;
  margin-right: 10rpx;
}
.file-info .count .dot1 {
  background: #d3d3d3;
}
.file-info .count .dot2 {
  background: #18aa49;
}
.file-info .count .dot3 {
  background: #ff0000;
}
</style>
