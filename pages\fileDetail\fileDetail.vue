<template>
  <view>
    <uv-navbar title="资产盘点" :fixed="true" :placeholder="true" :autoBack="true" :bgColor="$c.themeColor()" leftIconColor="#ffffff" :titleStyle="{ color: '#fff' }"></uv-navbar>
    <view class="file-info">
      <view class="tit">{{ text }}</view>
      <view class="count">
        <view class="i">
          <view class="num">{{ notCheckCount + checkCount }}</view>
          <view class="t">
            <view class="dot dot1"></view>
            总数
          </view>
        </view>
        <view class="i">
          <view class="num">{{ checkCount }}</view>
          <view class="t">
            <view class="dot dot2"></view>
            已盘点
          </view>
        </view>
        <view class="i">
          <view class="num">{{ notCheckCount }}</view>
          <view class="t">
            <view class="dot dot3"></view>
            未盘点
          </view>
        </view>
      </view>
    </view>
    <uv-tabs :list="tabs" @click="click" :current="current" :scrollable="false" :itemStyle="{ height: '38px' }" v-if="status == 1"></uv-tabs>
    <view class="panD-list" v-if="status == 1">
      <view class="li flex" @click="goDetail(item)" v-for="(item, index) in list">
        <view class="flex-bd">
          <view class="name">{{ item.name }}</view>
          <view class="no">资产编码:{{ item.code }}</view>
          <!-- <view class="tags">
            <view class="tag-cate">
              {{ item.AssetCateName }}
            </view>
          </view> -->
        </view>
        <view class="cell-ft">
          <uv-icon name="arrow-right" color="#999" :size="16"></uv-icon>
        </view>
      </view>
    </view>
    <view class="fixbtn">
      <view class="blank"></view>
      <view class="btns bor-t flex" v-if="status == 0">
        <view class="btn" @click="submit()">结束盘点</view>
      </view>
      <view class="btns bor-t flex" v-else>
        <view class="btn" style="background: #ddd; color: #999">已盘点</view>
      </view>
    </view>
  </view>
</template>

<script>
// #ifdef APP-PLUS
var pdamodule = uni.requireNativePlugin("uniplugin_pdamodule");
var officemodule = uni.requireNativePlugin("uniplugin_officemodule");
var testModule = uni.requireNativePlugin("uniplugin_rfid_module");
// #endif
export default {
  data() {
    return {
      tabs: [{ name: "已盘" }, { name: "未盘" }],
      current: 0,
      list: [],
      notCheckCount: 0,
      checkCount: 0,
      title: "",
      path: "",
      status: 0,
      checkStatus: 1,
      arrDate: [], //缓存扫码数据
      isPlay: false,
      innerAudioContext: null,
      isStop: false,
      lastScanTime: 0, // 上次扫码时间
      scanDebounceTime: 300, // 防抖时间(毫秒)
      isScanning: false, // 是否正在扫码
    };
  },
  onLoad(e) {
    var path = decodeURIComponent(e.path);
    console.log(path);
    this.text = e.text;
    this.path = path;
    console.log(officemodule);
    officemodule.getExcelData(
      {
        filePath: path,
      },
      (res) => {
        console.log(res);

        if (res.status == "success") {
          this.notCheckCount = res.data.notCheckCount;
          this.checkCount = res.data.checkCount;
          this.status = res.extdata;

          if (res.extdata == 1) {
            this.getList();
          }

          if (res.extdata == 0) {
            // #ifdef APP
            if (this.$c.isPda1()) {
              pdamodule.ClearCheckBillCode({}, (res) => {
                console.log(res);
                pdamodule.onRfidBack({}, (res) => {
                  console.log(res);
                  var RFIDCode = JSON.parse(res.data)[0].obj;
                  var data = [];
                  for (let i in RFIDCode) {
                    data.push(RFIDCode[i]);
                  }
                  console.log({
                    filePath: this.path,
                    numbers: RFIDCode,
                  });
                  //提交rfid盘点
                  officemodule.rfidCheck(
                    {
                      filePath: this.path,
                      numbers: RFIDCode,
                    },
                    (res) => {
                      console.log(res);
                      if ((res.status = "success")) {
                        this.notCheckCount = res.data.notCheckCount;
                        this.checkCount = res.data.checkCount;
                      }
                    }
                  );
                });
              });
            }
            console.log(this.$c.isPda2());

            // #endif
          }
        }
      }
    );

    if (this.$c.isPda2()) {
      // 初始化音频上下文
      this.innerAudioContext = uni.createInnerAudioContext();
      this.innerAudioContext.autoplay = false;
      this.innerAudioContext.loop = false; // 改为不循环播放
      this.innerAudioContext.src = "/static/music.mp3";

      this.innerAudioContext.onPlay(() => {
        console.log("开始播放扫码成功音效");
      });

      this.innerAudioContext.onEnded(() => {
        console.log("音效播放结束");
        this.isPlay = false;
      });

      this.innerAudioContext.onError((res) => {
        console.error("音频播放错误:", res.errMsg, res.errCode);
        this.isPlay = false;
      });

      //连接pda;
      testModule.Connect(
        {
          com: "/dev/ttyAS3",
          baud: 115200,
        },
        (res) => {
          uni.showToast({
            icon: "none",
            title: JSON.stringify(res),
          });

          plus.key.addEventListener("keydown", (e) => {
            if (e.keyCode === 134) {
              this.handleScanKeyPress();
            }
          });

          plus.key.addEventListener("keyup", (e) => {
            if (e.keyCode === 134) {
              this.handleScanKeyRelease();
            }
          });
        }
      );
    }
  },
  onHide() {
    // #ifdef APP
    if (this.$c.isPda1()) {
      pdamodule.StopBack({}, (res) => {});
    }
    // #endif
  },
  onUnload() {
    this.Unlink();
  },
  methods: {
    click(e) {
      console.log(e);
      if (e.name == "已盘") {
        this.checkStatus = 1;
      } else {
        this.checkStatus = 0;
      }
      this.getList();
    },
    getList() {
      officemodule.getCheckDetailList(
        {
          filePath: this.path,
          checkStatus: this.checkStatus,
        },
        (res) => {
          if (res.status == "success") {
            for (let i in res.data) {
              res.data[i].name = res.data[i]["资产名称"];
              res.data[i].code = res.data[i]["资产编码"];
            }
            this.list = res.data;
          }
        }
      );
    },
    submit() {
      uni.showModal({
        title: "提示",
        content: "确定结束盘点吗？",
        success: (res) => {
          if (res.confirm) {
            // #ifdef APP-PLUS
            officemodule.endCheck(
              {
                filePath: this.path,
              },
              (res) => {
                console.log(res);
              }
            );
            // #endif
            uni.navigateBack();
          } else if (res.cancel) {
            console.log("用户点击取消");
          }
        },
      });
    },

    //////////////////////pda2方法开始
    // 处理扫码按键按下
    handleScanKeyPress() {
      const currentTime = Date.now();

      // 防抖处理：如果距离上次扫码时间太短，则忽略
      if (currentTime - this.lastScanTime < this.scanDebounceTime) {
        return;
      }

      this.isStop = false;

      // 如果没有正在扫码，则开始扫码
      if (!this.isScanning) {
        this.isScanning = true;
        this.lastScanTime = currentTime;
        this.performScan();
      }
    },

    // 处理扫码按键释放
    handleScanKeyRelease() {
      this.isStop = true;
      this.isScanning = false;
      console.log("释放按钮");
      console.log({
        filePath: this.path,
        numbers: this.arrDate,
      });
      var form = {
        filePath: this.path,
        numbers: this.arrDate,
      };
      //提交rfid盘点
      officemodule.rfidCheck(JSON.parse(JSON.stringify(form)), (res) => {
        console.log(this.arrDate);
        console.log(res);
        if ((res.status = "success")) {
          this.notCheckCount = res.data.notCheckCount;
          this.checkCount = res.data.checkCount;
        }
      });
    },

    // 执行扫码操作
    async performScan() {
      try {
        await this.Inventory();
      } catch (error) {
        console.error("扫码失败:", error);
        this.isScanning = false;
      }
    },

    // 检查数据是否重复
    isDuplicateData(data) {
      // 转换为字符串并检查
      const dataStr = String(data || "").trim();
      if (!dataStr) {
        return true; // 空数据视为重复
      }
      return this.arrDate.includes(dataStr);
    },

    // 处理EPC数据，去掉空格
    processEpcData(epcStr) {
      if (!epcStr) return "";
      // 移除空格并转换为大写
      return epcStr.replace(/\s+/g, "").toUpperCase();
    },

    // 添加数据到缓存
    addToCache(data) {
      // 转换为字符串并检查
      const dataStr = String(data || "").trim();
      if (dataStr && !this.isDuplicateData(data)) {
        this.arrDate.push(dataStr);
        // 限制缓存大小，避免内存溢出
        if (this.arrDate.length > 1000) {
          this.arrDate.shift(); // 移除最旧的数据
        }
        return true; // 新数据
      }
      return false; // 重复或无效数据
    },

    // 播放扫码成功音效
    playScanSuccessSound() {
      if (this.innerAudioContext && !this.isPlay) {
        this.isPlay = true;
        this.innerAudioContext.play();

        // 播放一段时间后停止
        setTimeout(() => {
          this.innerAudioContext.pause();
          this.isPlay = false;
        }, 500); // 播放500毫秒
      }
    },

    // 清空扫码缓存
    clearScanCache() {
      this.arrDate = [];
      uni.showToast({
        icon: "success",
        title: "缓存已清空",
        duration: 1000,
      });
      console.log("扫码缓存已清空");
    },

    // 查看缓存详情
    showCacheDetails() {
      if (this.arrDate.length === 0) {
        uni.showToast({
          icon: "none",
          title: "缓存为空",
          duration: 1000,
        });
        return;
      }

      // 显示最近的10条缓存数据
      const recentData = this.arrDate.slice(-10);
      const content = recentData.map((item, index) => `${this.arrDate.length - 10 + index + 1}. ${item}`).join("\n");

      uni.showModal({
        title: `缓存详情 (共${this.arrDate.length}条)`,
        content: `最近10条数据:\n${content}`,
        showCancel: false,
        confirmText: "确定",
      });
    },

    Unlink() {
      testModule.Disconnect((res) => {
        console.log(res);
      });
    },

    async Inventory() {
      console.log("开始扫码...");

      return new Promise((resolve, reject) => {
        testModule.SetCmd(
          {
            FunName: "startInventory",
            fastSwitchMode: false,
            session: 1,
            target: 0,
            Phase: false,
          },
          (res) => {
            try {
              //console.log("扫码结果: " + JSON.stringify(res));
              const inventoryData = JSON.parse(JSON.stringify(res));
              const inventoryTags = inventoryData.inventoryTag;

              // 检查是否有标签数据
              if (inventoryTags && Array.isArray(inventoryTags) && inventoryTags.length > 0) {
                // 只要扫到数据就播放音效
                this.playScanSuccessSound();

                let newTagCount = 0;
                let duplicateTagCount = 0;
                let processedTags = [];

                // 处理每个标签
                inventoryTags.forEach((tag, index) => {
                  if (tag && tag.epc) {
                    // 处理EPC数据，去掉空格
                    const cleanEpc = this.processEpcData(tag.epc);
                    processedTags.push({
                      original: tag.epc,
                      clean: cleanEpc,
                      rssi: tag.rssi,
                      freq: tag.freq,
                    });

                    // 检查是否为新数据（使用处理后的EPC数据进行缓存）
                    const isNewData = this.addToCache(cleanEpc);

                    if (isNewData) {
                      newTagCount++;
                      console.log(`发现新标签 ${index + 1}: ${tag.epc} -> ${cleanEpc}`);
                    } else {
                      duplicateTagCount++;
                      console.log(`重复标签 ${index + 1}: ${tag.epc} -> ${cleanEpc}`);
                    }
                  }
                });

                // 显示提示信息
                if (newTagCount > 0) {
                  uni.showToast({
                    icon: "none",
                    title: `新增 ${newTagCount} 个标签`,
                    duration: 1500,
                  });
                } else {
                  // uni.showToast({
                  //   icon: "none",
                  //   title: `全部为重复标签`,
                  //   duration: 1500,
                  // });
                }
                // 打印处理后的标签信息
                console.log("处理后的标签:", processedTags);
              } else {
                console.log("未扫描到标签");
                // 无数据时不播放音效
              }

              this.isScanning = false;
              resolve(inventoryData);
            } catch (error) {
              console.error("处理扫码结果失败:", error);
              this.isScanning = false;
              reject(error);
            }
          }
        );
      });
    },
    //////////////////////pda2方法结束
  },
};
</script>

<style>
.file-info {
  padding: 30rpx;
}
.file-info .tit {
  margin-bottom: 20rpx;
  font-size: 28rpx;
}
.file-info .count {
  background: #fff;
  display: flex;
  padding: 30rpx 0;
}
.file-info .count .i {
  flex: 1;
  text-align: center;
}
.file-info .count .num {
  font-size: 36rpx;
}
.file-info .count .t {
  font-size: 24rpx;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10rpx;
}
.file-info .count .dot {
  width: 10rpx;
  height: 10rpx;
  border-radius: 50%;
  margin-right: 10rpx;
}
.file-info .count .dot1 {
  background: #d3d3d3;
}
.file-info .count .dot2 {
  background: #18aa49;
}
.file-info .count .dot3 {
  background: #ff0000;
}
</style>
