## Waterfall 瀑布流

> **组件名：uv-waterfall**

该组件主要用于瀑布流式布局显示，视觉表现为参差不齐的多栏布局，随着页面滚动条向下滚动，这种布局还会不断加载数据块并附加至当前尾部，同时集成`nvue`的原生瀑布流用于`app-nvue`。常用于一些电商商品展示等，如某宝首页、x红书等。

研究uniapp瀑布流多年，**该方式是目前小程序端最佳方案**，灵活配置，简单易用，开箱即用。

该插件请根据文档耐心查看，`vue`的写法稍微麻烦点，但是效果是很好的，比之前上线的两个版本的瀑布流适用，更有扩展性，我自己的上线项目也是用的此插件。

# <a href="https://www.uvui.cn/components/waterfall.html" target="_blank">查看文档</a>

## [下载完整示例项目](https://ext.dcloud.net.cn/plugin?name=uv-ui)

### [更多插件，请关注uv-ui组件库](https://ext.dcloud.net.cn/plugin?name=uv-ui)

![image](https://mp-a667b617-c5f1-4a2d-9a54-683a67cff588.cdn.bspapp.com/uv-ui/banner.png)

#### 如使用过程中有任何问题反馈，或者您对uv-ui有一些好的建议，欢迎加入uv-ui官方交流群：<a href="https://www.uvui.cn/components/addQQGroup.html" target="_blank">官方QQ群</a>
