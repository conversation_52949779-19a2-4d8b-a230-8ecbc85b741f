<template>
  <view>
    <uv-navbar title="资产管理" :fixed="true" :placeholder="true" :autoBack="true" :bgColor="$c.themeColor()" leftIconColor="#ffffff" :titleStyle="{ color: '#fff' }"></uv-navbar>
    <view class="scroll-content">
      <view class="fixed-tab-height">
        <view class="fixed-tab" :style="'top: ' + divTop + 'px;'">
          <view class="top-search flex">
            <view class="flex-item">
              <uv-search shape="square" :height="28" :showAction="false" placeholder="请输入资产编号" v-model="searchForm.AssetNumber" @search="search"></uv-search>
            </view>
            <view class="flex-hd" @click="popShow = true">
              <i class="iconfont icon-shaixuan"></i>
            </view>
          </view>
        </view>
      </view>
      <view class="scroll">
        <view class="panD-list">
          <view class="li flex" v-for="(item, index) in list" :key="index" @click="$c.navigateTo('../assets/detail?assetNumber=' + item.AssetNumber)">
            <view class="img">
              <image v-if="item.Smallimage" class="cover" :src="$c.getImageUrl(item.Smallimage)" mode="aspectFill" @click.stop="$c.viewImage(item.Smallimage)"></image>
              <image v-else class="cover" src="/static/images/sbnopic.png"></image>
              <view :class="'tag tag' + item.AssetStatus">{{ item.AssetStatusCaption }}</view>
            </view>
            <view class="flex-bd">
              <view class="name">{{ item.AssetName }}</view>
              <view class="no">编号:{{ item.AssetNumber }}</view>
              <view class="tags">
                <view class="tag-cate">
                  {{ item.AssetCateName }}
                </view>
              </view>
            </view>
            <view class="cell-ft">
              <uv-icon name="arrow-right" color="#999" :size="16"></uv-icon>
            </view>
          </view>
        </view>
      </view>
    </view>
    <common-filter v-model="searchForm" :show="popShow" :filter-items="searchFilter" @close="popShow = false" @confirm="search" @reset="search" />
    <view class="pd30">
      <uv-load-more
        fontSize="12"
        color="#999"
        :status="loadmore.status"
        :loading-text="loadmore.loadingText"
        :loadmore-text="loadmore.loadmoreText"
        :nomore-text="loadmore.nomoreText" />
    </view>
    <uv-back-top
      :duration="0"
      :scroll-top="scrollTop"
      mode="square"
      :icon-style="{ fontSize: '32rpx', color: '#fff' }"
      :custom-style="'background-color:' + $c.themeColor()"></uv-back-top>
    <view class="fixed-add" @click="$c.navigateTo('../assets/add?type=add')">
      <view class="inline"><uv-icon name="plus" :size="20" color="#fff"></uv-icon></view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      divTop: 0,
      popShow: false,
      loadmore: {
        status: "loading",
        loadingText: "努力加载中",
        loadmoreText: "轻轻上拉",
        nomoreText: "没有更多了",
      },
      page: 1,
      scrollTop: 0,
      list: [],
      searchForm: {},
      searchFilter: [
        { name: "AssetNumber", type: "input", label: "资产编号" },
        { name: "AssetName", type: "input", label: "资产名称" },
        { name: "AssetCateName", value: "AssetCateCode", type: "common-picker", pickerType: "category", label: "资产分类" },
        { name: "OrgName", type: "input", label: "管理部门" },
        { name: "WarehouseName", type: "input", label: "仓库" },
        { name: "UseDeptName", type: "input", label: "使用部门" },
        { name: "ChargeUserName", type: "input", label: "管理人" },
        { name: "UseUserName", type: "input", label: "使用人" },
        { name: "AssetStatusName", value: "AssetStatus", type: "common-picker", pickerType: "status", label: "资产状态" },
        { name: "LocationName", type: "input", label: "位置" },
      ],
    };
  },
  onLoad() {
    var sys = uni.getSystemInfoSync();
    this.divTop = sys.statusBarHeight + 44;
    this.getList(1).then((res) => {
      this.list = res;
    });

    uni.$on("refreshList", () => {
      this.search();
    });
  },
  onUnload() {
    uni.$off("refreshList");
  },
  methods: {
    search(e) {
      this.page = 1;
      this.loadmore.status = "loading";
      this.list = [];
      this.getList(1).then((res) => {
        this.list = res;
      });
      if (e) {
        uni.pageScrollTo({
          scrollTop: 0,
        });
      }
    },
    getList(page) {
      return new Promise((resolve) => {
        this.$apis
          .getAssetsList(
            {
              PageIndex: page,
              PageSize: 20,
              LoginUserCode: uni.getStorageSync("UserCode"),
              ...this.searchForm,
            },
            {
              custom: {
                loading: false,
              },
            }
          )
          .then((res) => {
            if (res.data.length < 20) {
              this.loadmore.status = "nomore";
            }
            resolve(res.data);
          });
      });
    },
  },
  onReachBottom() {
    if (this.loadmore.status == "nomore") {
      return;
    }
    var list = this.list;
    this.getList(this.page + 1).then((res) => {
      for (let index in res) {
        list.push(res[index]);
      }
      if (res.length > 0) {
        this.list = list;
        this.page++;
        if (res.length < 20) {
          this.loadmore.status = "nomore";
        }
      }
    });
  },
  onPageScroll(e) {
    this.scrollTop = e.scrollTop;
  },
};
</script>

<style></style>
