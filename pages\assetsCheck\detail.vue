<template>
  <view>
    <uv-navbar title="资产盘点" :fixed="true" :placeholder="true" :autoBack="true" :bgColor="$c.themeColor()" leftIconColor="#ffffff" :titleStyle="{ color: '#fff' }"></uv-navbar>
    <view class="pd-info">
      <view class="pannel">
        <view class="tit bor-b flex">
          <view class="flex-bd">单据信息</view>
          <view class="flex-hd">
            <view :class="'tag check-tag' + info.WfInstanceStatus">{{ info.WfStatusCaption }}</view>
          </view>
        </view>
        <view class="pd030">
          <!-- 易耗品入库 -->
          <view class="form-cell-group">
            <view class="form-cell flex bor-b">
              <view class="cell-label">单据号</view>
              <view class="cell-value">{{ info.BillNo }}</view>
            </view>
            <view class="form-cell flex bor-b">
              <view class="cell-label">盘点主题</view>
              <view class="cell-value">{{ info.CheckTitle }}</view>
            </view>
            <view class="form-cell flext bor-b">
              <view class="cell-label">盘点管理员</view>
              <view class="cell-value">{{ info.CheckUserNameCaption }}</view>
            </view>
            <view class="form-cell flex bor-b">
              <view class="cell-label">截止日期</view>
              <view class="cell-value">{{ info.OverDateTimeCaption || "--" }}</view>
            </view>
            <view class="form-cell flext bor-b">
              <view class="cell-label">资产分类</view>
              <view class="cell-value">{{ info.AssetCateNamesCaption || "--" }}</view>
            </view>
            <view class="form-cell flext bor-b">
              <view class="cell-label">位置</view>
              <view class="cell-value">{{ info.LocationNamesCaption || "--" }}</view>
            </view>
            <view class="form-cell flext bor-b">
              <view class="cell-label">仓库</view>
              <view class="cell-value">{{ info.WareHouseNamesCaption || "--" }}</view>
            </view>
            <view class="form-cell flext bor-b">
              <view class="cell-label">管理部门</view>
              <view class="cell-value">{{ info.ManageDeptNamesCaption || "--" }}</view>
            </view>
            <view class="form-cell flext bor-b">
              <view class="cell-label">使用部门</view>
              <view class="cell-value">{{ info.UseDeptNamesCaption || "--" }}</view>
            </view>
            <view class="form-cell flext bor-b">
              <view class="cell-label">可修改字段</view>
              <view class="cell-value">{{ info.ModifyFiledCaption || "--" }}</view>
            </view>
            <view class="form-cell flext">
              <view class="cell-label">备注</view>
              <view class="cell-value">{{ info.Remark || "--" }}</view>
            </view>
          </view>
        </view>
      </view>
      <block v-if="type == 'audit'">
        <view class="hr"></view>
        <view class="pannel">
          <view class="tit flex bor-b">
            <view class="flex-bd">单据详情</view>
          </view>
          <view class="pd30">
            <u-button type="info" :customStyle="{ borderColor: $c.themeColor(), color: $c.themeColor() }" @click="$c.navigateTo('../assetsCheck/checkList?Code=' + code)">
              <view class="inline"><u-icon name="search" :color="$c.themeColor()" :size="18"></u-icon></view>
              <view class="inline">查看详情</view>
            </u-button>
          </view>
        </view>
      </block>
      <common-audit-progress :code="code" v-if="info.WfInstanceStatus > -1" />
    </view>
    <view class="fixbtn" v-if="info.WfInstanceStatus == -1">
      <view class="blank"></view>
      <view class="btns bor-t flex">
        <view class="btn" @click="submit()">提交</view>
        <view class="btn btn-bor" @click="$c.navigateTo('../assetsChange/add?type=edit&Code=' + info.Code)">修改</view>
      </view>
    </view>
    <common-approve :code="code" :taskCode="taskCode" api="approveAssetCheck" v-if="info.WfInstanceStatus == 0 && type == 'audit'" />
  </view>
</template>

<script>
export default {
  data() {
    return {
      info: "",
      assetList: "",
      type: "",
      code: "",
      taskCode: "",
    };
  },
  onLoad(e) {
    this.code = e.Code;
    this.type = e.type;
    this.taskCode = e.taskCode;
    this.detail();

    uni.$on("refreshDetail", () => {
      this.detail();
    });
  },
  onUnload() {
    // 页面销毁时取消事件监听
    uni.$off("refreshDetail");
  },
  methods: {
    detail() {
      this.$apis
        .getAssetCheckDetail({
          Code: this.code,
        })
        .then((res) => {
          this.info = res.data;
          //   this.assetList = res.data.AssetChangeDetail;
        });
    },
    submit() {
      this.$c.submitAsset(this.info.Code, "submitAssetChange");
    },
  },
};
</script>

<style lang="scss"></style>
