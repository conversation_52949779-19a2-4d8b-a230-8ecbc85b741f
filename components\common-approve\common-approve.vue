<template>
  <view class="bot-form">
    <view class="form">
      <view class="tit bor-b">审批</view>
      <uv-form ref="uForm" labelWidth="85">
        <common-picker
          :borderBottom="false"
          required
          label="审批结果"
          pickerType="radio"
          v-model="approveForm.DealStatus"
          :radioOptions="[
            { name: '通过', value: 1 },
            { name: '不通过', value: 2 },
          ]"
          v-model:displayValue="approveForm.DealStatusName"></common-picker>
        <uv-form-item label="审批意见">
          <uv-textarea v-model="approveForm.DealOption" :cursorSpacing="50" placeholder="请输入"></uv-textarea>
        </uv-form-item>
        <uv-button type="primary" :customStyle="{ background: $c.themeColor(), color: '#fff', marginTop: '30rpx' }" @click="approve()">确定</uv-button>
      </uv-form>
    </view>
    <view class="blank"></view>
  </view>
</template>

<script>
export default {
  name: "common-approve",

  props: {
    code: {
      type: String,
      default: false,
    },
    taskCode: {
      type: String,
      default: "",
    },
    api: {
      type: String,
      default: "",
    },
  },

  data() {
    return {
      approveForm: {
        Code: "", //主键编码
        DealStatus: -1, //1 通过 2-不通过
        DealStatusName: "",
        DealOption: "",
        LoginUserCode: uni.getStorageSync("UserCode"),
      },
    };
  },

  created() {
    this.approveForm.Code = this.code;
    this.approveForm.TaskCode = this.taskCode;
  },

  methods: {
    approve() {
      if (!this.approveForm.DealStatusName) {
        return uni.$uv.toast("请选择审批结果");
      }
      this.$apis[this.api](this.approveForm).then((res) => {
        if (res.code == 100) {
          uni.showToast({
            title: "审批成功",
          });
          setTimeout(() => {
            uni.navigateBack();
            uni.$emit("refreshList");
          }, 1500);
        } else {
          uni.$uv.toast(res.msg);
        }
      });
    },
  },
};
</script>

<style lang="scss"></style>
