<template>
  <view>
    <uv-navbar title="离线盘点" :fixed="true" :placeholder="true" :autoBack="false" :bgColor="$c.themeColor()" :titleStyle="{ color: '#fff' }">
      <template v-slot:left><view></view></template>
    </uv-navbar>
    <view class="file-list">
      <view class="item flex" @click="$c.navigateTo('../fileDetail/fileDetail?path=' + item.value + '&text=' + item.text)" v-for="(item, index) in fileList">
        <view class="icon"><uv-icon name="file-text-fill" color="#1677ff" :size="24"></uv-icon></view>
        <view class="name flex-bd">{{ item.text }}</view>
        <view class="btn">
          <uv-button type="primary" plain size="mini" customStyle="font-size:26rpx">开始盘点</uv-button>
        </view>
      </view>
    </view>
    <view v-if="fileList.length == 0" style="text-align: center; padding: 100rpx 0">
      <image style="width: 200rpx; height: 200rpx" src="/static/images/nores.png"></image>
      <view class="text" style="font-size: 28rpx; padding-top: 30rpx; color: #999">暂无数据</view>
    </view>

    <!-- <navigator url="/pages/test/test">123</navigator> -->
  </view>
</template>

<script>
// #ifdef APP-PLUS
var pdamodule = uni.requireNativePlugin("uniplugin_pdamodule");
var officemodule = uni.requireNativePlugin("uniplugin_officemodule");
// #endif
export default {
  data() {
    return {
      fileList: [],
    };
  },
  onLoad() {
    // #ifdef APP-PLUS
    //初始化盘点机
    if (this.$c.isPda1()) {
      pdamodule.InitPda((res) => {
        console.log(res);
        pdamodule.InitBar({}, (res) => {
          console.log(res);
          pdamodule.InitKeyReciver((res) => {
            console.log(res);
          });
        });
      });
    }
    console.log(officemodule);
    officemodule.getFileList({}, (res) => {
      console.log(res);
      if (res.status == "success") {
        for (let i in res.data) {
          res.data[i].value = encodeURIComponent(res.data[i].value);
        }
        this.fileList = res.data;
      }
    });
    // #endif
  },
  methods: {},
};
</script>

<style lang="scss">
.file-list .item {
  background: #fff;
  margin: 30rpx;
  padding: 30rpx;
  font-size: 28rpx;
  border-radius: 4px;
}
.file-list .item .icon {
  margin-right: 10rpx;
}
.file-list .item .name {
  margin-right: 10rpx;
}
</style>
