@import "uview-ui/theme.scss";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* 颜色变量 */

/* 行为相关颜色 */

/* 文字基本颜色 */
$uni-text-color: #2f363c; //基本色
$uni-text-color-inverse: #fff; //反色
$uni-text-color-grey: #999; //辅助灰色，如加载更多的提示信息
$uni-text-color-placeholder: #808080;
$uni-text-color-disable: #c0c0c0;

/* 背景颜色 */
$uni-bg-color: #ffffff;
$uni-bg-color-grey: #f8f8f8;
$uni-bg-color-hover: #f1f1f1; //点击状态颜色
$uni-bg-color-mask: rgba(0, 0, 0, 0.4); //遮罩颜色

/* 边框颜色 */
$uni-border-color: #c8c7cc;

/* 尺寸变量 */

/* 文字尺寸 */
$uni-font-size-sm: 12px;
$uni-font-size-base: 14px;
$uni-font-size-lg: 16px;

/* 图片尺寸 */
$uni-img-size-sm: 20px;
$uni-img-size-base: 26px;
$uni-img-size-lg: 40px;

/* Border Radius */
$uni-border-radius-sm: 2px;
$uni-border-radius-base: 3px;
$uni-border-radius-lg: 6px;
$uni-border-radius-circle: 50%;

/* 水平间距 */
$uni-spacing-row-sm: 5px;
$uni-spacing-row-base: 10px;
$uni-spacing-row-lg: 15px;

/* 垂直间距 */
$uni-spacing-col-sm: 4px;
$uni-spacing-col-base: 8px;
$uni-spacing-col-lg: 12px;

/* 透明度 */
$uni-opacity-disabled: 0.3; // 组件禁用态的透明度

/* 文章场景相关 */
$uni-color-title: #2c405a; // 文章标题颜色
$uni-font-size-title: 20px;
$uni-color-subtitle: #555555; // 二级标题颜色
$uni-font-size-subtitle: 26px;
$uni-color-paragraph: #3f536e; // 文章段落颜色
$uni-font-size-paragraph: 15px;
/*自定义*/
$uni-color-main: #1677ff;
$uni-color-main-light: #5ba0ff;
$uni-color-main-lighter: #b2cbf4;
$uni-color-main-lightest: #f5f8fd;
/*辅助色*/
$uni-color-danger: #f53f3f;
$uni-color-danger-light: #f87878;
$uni-color-danger-lighter: #fcc5c5;
$uni-color-danger-lightest: #fef5f5;

$uni-color-warning: #ff7d00;
$uni-color-warning-light: #ffa44c;
$uni-color-warning-lighter: #ffd8b2;
$uni-color-warning-lightest: #fff8f2;

$uni-color-success: #00b42a;
$uni-color-success-light: #4cca69;
$uni-color-success-lighter: #b2e8bf;
$uni-color-success-lightest: #f2fbf4;

$uni-color-yellow: #ffbb00;
$uni-color-yellow-light: #ffcf4c;
$uni-color-yellow-lighter: #fff7e1;
$uni-color-yellow-lightest: #fffcf5;

/*基础色*/
$uni-color-white: #ffffff;
$uni-color-black: #000000;
$uni-color-gray: #e2e2e2;
$uni-color-gray-nomal: #5f5f5f;
$uni-color-gray-nomaler: #dddddd;
$uni-color-gray-light: #eeeeee;
$uni-color-gray-lighter: #f6f6f6;
$uni-color-gray-lightest: #fafafa;
$uni-color-gray-dark: #d2d2d2;
$uni-color-gray-darker: #c2c2c2;
$uni-color-gray-darkest: #333333;
$uni-color-gray-master: #999999;
$uni-color-gray-mastest: #666666;
/*中性色*/
$uni-color-base: #2f363c;
$uni-color-base-darker: #23292e;
$uni-color-base-darkest: #1c1f23;
