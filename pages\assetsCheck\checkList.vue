<template>
  <view>
    <uv-navbar title="资产盘点" :fixed="true" :placeholder="true" :autoBack="true" :bgColor="$c.themeColor()" leftIconColor="#ffffff" :titleStyle="{ color: '#fff' }"></uv-navbar>
    <view class="scroll-content">
      <view class="fixed-tab-height">
        <view class="fixed-tab" :style="'top: ' + divTop + 'px;'">
          <uv-tabs :list="tabs" @click="click" :current="current" :scrollable="false" :itemStyle="{ height: '38px' }"></uv-tabs>
          <view class="top-search flex bor-t">
            <view class="flex-item">
              <uv-search shape="square" :height="28" :showAction="false" placeholder="请输入资产编号" v-model="searchForm.AssetNumber" @search="search"></uv-search>
            </view>
            <view class="flex-hd" @click="popShow = true">
              <i class="iconfont icon-shaixuan"></i>
            </view>
          </view>
        </view>
      </view>
      <view class="scroll" style="padding-top: 38px">
        <view class="panD-list">
          <view class="li flex" @click="goDetail(item)" v-for="(item, index) in list">
            <view class="img">
              <image v-if="item.Smallimage" mode="aspectFill" class="cover" :src="$c.getImageUrl(item.Smallimage)"></image>
              <image v-else class="cover" src="../../static/images/sbnopic.png"></image>
              <view :class="'tag tag' + item.AssetStatus">{{ item.AssetStatusCaption }}</view>
            </view>
            <view class="flex-bd">
              <view class="name">{{ item.AssetName }}</view>
              <view class="no">编号:{{ item.AssetNumber }}</view>
              <view class="tags">
                <view class="tag-cate">
                  {{ item.AssetCateName }}
                </view>
              </view>
            </view>
            <view class="cell-ft">
              <u-icon name="arrow-right" color="#999" :size="16"></u-icon>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view style="padding: 16rpx 0">
      <uv-load-more
        fontSize="12"
        color="#999"
        :status="loadmore.status"
        :loading-text="loadmore.loadingText"
        :loadmore-text="loadmore.loadmoreText"
        :nomore-text="loadmore.nomoreText" />
    </view>
    <view class="fixbtn" v-if="info.AssetCheckStatus == 0 && info.WfInstanceStatus == 1">
      <view class="blank"></view>
      <view class="btns bor-t flex">
        <view class="btn btn-bor flex1" @click="$c.navigateTo('../assetsProfit/add?AssetCheckCode=' + Code + '&type=add')">
          <view class="inline"><uv-icon name="plus" :color="$c.themeColor()" :size="16"></uv-icon></view>
          <view class="inline">新增盘盈</view>
        </view>
        <view class="btn btn-bor flex1" @click="endCheckShow = true">
          <view class="inline"><uv-icon name="close" :color="$c.themeColor()" :size="16"></uv-icon></view>
          <view class="inline">结束盘点</view>
        </view>
        <view class="btn flex1" @click="qrCode()">
          <view class="inline"><uv-icon name="scan" color="#fff" :size="16"></uv-icon></view>
          <view class="inline">扫码盘点</view>
        </view>
      </view>
    </view>
    <common-filter v-model="searchForm" :show="popShow" :filter-items="searchFilter" @close="popShow = false" @confirm="search" @reset="search" />
    <uv-back-top
      :scroll-top="scrollTop"
      mode="square"
      :duration="0"
      :iconStyle="{
        fontSize: '32rpx',
        color: '#fff',
      }"
      :customStyle="'background-color:' + $c.themeColor()"></uv-back-top>
  </view>
</template>

<script>
// #ifdef APP-PLUS
var pdamodule = uni.requireNativePlugin("uniplugin_pdamodule");
var qrmodule = uni.requireNativePlugin("uniplugin_QRCodeModule");
// #endif
export default {
  data() {
    return {
      divTop: 0,
      loadmore: {
        status: "loading",
        loadingText: "努力加载中",
        loadmoreText: "轻轻上拉",
        nomoreText: "没有更多了",
      },
      list: [],
      page: 1,
      scrollTop: 0,
      tabs: [
        { name: "未盘", badge: { value: 0 } },
        { name: "已盘", badge: { value: 5 } },
        { name: "盘亏", badge: { value: 0 } },
        { name: "盘盈", badge: { value: 0 } },
      ],
      current: 0,
      popShow: false,
      searchForm: {
        AssetCheckStatus: "",
        CheckStatus: 0,
      },
      searchFilter: [
        { name: "AssetNumber", type: "input", label: "资产编号" },
        { name: "AssetName", type: "input", label: "资产名称" },
        { name: "AssetCateName", value: "AssetCateCode", type: "common-picker", pickerType: "category", label: "资产分类" },
        { name: "OrgName", type: "input", label: "管理部门" },
        { name: "WarehouseName", type: "input", label: "所属仓库" },
        { name: "UseDeptName", type: "input", label: "使用部门" },
        { name: "ChargeUserName", type: "input", label: "管理人" },
        { name: "UseUserName", type: "input", label: "使用人" },
        { name: "AssetStatusName", value: "AssetStatus", type: "common-picker", pickerType: "status", label: "资产状态" },
        { name: "LocationName", type: "input", label: "存放地点" },
        // { name: "BuyDate", value: "BuyDateValue", type: "daterange", label: "购置日期" },
        // { name: "UseDate", value: "UseDateValue", type: "daterange", label: "使用日期" },
      ],
      info: {},
      endCheckShow: false,
    };
  },
  onLoad(e) {
    this.Code = e.Code;
    var sys = uni.getSystemInfoSync();
    this.divTop = sys.statusBarHeight + 44;

    this.getList(1).then((res) => {
      this.list = res;
    });

    //盘点数量
    this.getCount();

    //获取盘盈数量
    this.getProfitList().then((res) => {});
    // #ifdef APP
    if (this.$c.isPda()) {
      pdamodule.ClearCheckBillCode({}, (res) => {
        console.log(res);
        pdamodule.onRfidBack({}, (res) => {
          console.log(res);
          var RFIDCode = JSON.parse(res.data)[0].obj;
          var data = [];
          for (let i in RFIDCode) {
            data.push(RFIDCode[i]);
          }
          console.log({
            AssetCheckCode: this.Code, //盘点编码
            LoginUserCode: uni.getStorageSync("UserCode"),
            Rfid: data.toString(),
          });
          //提交rfid盘点
          this.$apis
            .submitAssetCheckRfid({
              AssetCheckCode: this.Code, //盘点编码
              LoginUserCode: uni.getStorageSync("UserCode"),
              Rfid: data.toString(),
            })
            .then((res) => {
              console.log(res);
              uni.$uv.toast(res.msg);
              if (res.code == 100) {
                this.search();
                this.getCount();
              }
            });
        });
      });
    }
    // #endif

    //选择设备返回主页后
    uni.$on("assetsCheckSuccess", () => {
      this.search();
      this.getCount();
    });
    uni.$on("refreshProfitList", () => {
      this.getProfitList(1).then((res) => {
        if (this.current == 3) {
          this.list = [];
          this.page = 1;
          this.loadmore.status = "loading";
        }
      });
    });
  },
  onUnload() {
    // 页面销毁时取消事件监听
    uni.$off("assetsCheckSuccess");
    uni.$off("refreshProfitList");
  },
  onShow() {
    var that = this;

    // #ifdef APP
    if (this.$c.isPda()) {
      pdamodule.StartBack({}, (res) => {
        console.log(res);
        pdamodule.onBarBack({}, (res) => {
          console.log(res);
          var assetNumber = JSON.parse(res.data)[0].barCode;
          this.$apis
            .getAssetCheckDetailInfo({
              AssetNumber: assetNumber, //资产编码
              AssetCheckCode: this.Code, //盘点编码
            })
            .then((res) => {
              console.log(res);
              if (res.code == 100) {
                uni.navigateTo({
                  url:
                    "../assetsCheck/checkDetail?code=" +
                    this.Code +
                    "&assetNumber=" +
                    assetNumber +
                    "&modifyFiled=" +
                    this.info.ModifyFiled +
                    "&type=check&checkStatus=" +
                    this.info.AssetCheckStatus,
                });
              } else {
                uni.$uv.toast(res.msg);
              }
            });
        });
      });
    }
    // #endif
  },
  onHide() {
    // #ifdef APP
    if (this.$c.isPda()) {
      pdamodule.StopBack({}, (res) => {});
    }
    // #endif
  },
  methods: {
    getCount() {
      this.$apis
        .getAssetCheckDetail({
          Code: this.Code,
        })
        .then((res) => {
          this.info = res.data;
          this.tabs[0].badge.value = this.info.NCheckQuantityCaption;
          this.tabs[1].badge.value = this.info.YCheckQuantityCaption;
          this.tabs[2].badge.value = this.info.PKCheckQuantityCaption;
        });
      this.$apis
        .getAssetCheckProfitList({
          AssetCheckCode: this.Code,
        })
        .then((res) => {
          this.tabs[3].badge.value = res.page.TotalCount;
        });
    },
    search() {
      this.list = [];
      this.page = 1;
      this.loadmore.status = "loading";
      this[this.current == 3 ? "getProfitList" : "getList"](1).then((res) => {
        this.list = res;
      });
    },
    qrCode() {
      console.log("qrCode");
      // #ifdef APP-PLUS
      qrmodule.OpenQRCode(
        {
          type: "scan",
        },
        (res) => {
          qrmodule.onQRCodeBack({}, (res) => {
            var assetNumber = res.data;
            this.$apis
              .getAssetCheckDetailInfo({
                AssetNumber: assetNumber, //资产编码
                AssetCheckCode: this.Code, //盘点编码
              })
              .then((res) => {
                if (res.code == 100) {
                  uni.navigateTo({
                    url:
                      "../assetsCheck/checkDetail?code=" +
                      this.Code +
                      "&assetNumber=" +
                      assetNumber +
                      "&modifyFiled=" +
                      this.info.ModifyFiled +
                      "&type=check&checkStatus=" +
                      this.info.AssetCheckStatus,
                  });
                } else {
                  uni.$uv.toast(res.msg);
                }
              });
          });
        }
      );
      // #endif

      // #ifdef MP-WEIXIN
      uni.scanCode({
        success: (res) => {
          var assetNumber = res.result;
          this.$apis
            .getAssetCheckDetailInfo({
              AssetNumber: assetNumber, //资产编码
              AssetCheckCode: this.Code, //盘点编码
            })
            .then((res) => {
              if (res.code == 100) {
                uni.navigateTo({
                  url:
                    "../assetsCheck/checkDetail?code=" +
                    this.Code +
                    "&assetNumber=" +
                    assetNumber +
                    "&modifyFiled=" +
                    this.info.ModifyFiled +
                    "&type=check&checkStatus=" +
                    this.info.AssetCheckStatus,
                });
              } else {
                uni.$uv.toast(res.msg);
              }
            });
        },
      });
      // #endif
    },
    goDetail(e) {
      if (this.info.WfInstanceStatus == 0) {
        return;
      }
      if (this.current == 3) {
        uni.navigateTo({
          url: "../assetsCheck/checkDetail?code=" + this.Code + "&detailCode=" + e.Code + "&type=profit&checkStatus=" + this.info.AssetCheckStatus,
        });
      } else {
        uni.navigateTo({
          url:
            "../assetsCheck/checkDetail?code=" +
            this.Code +
            "&assetNumber=" +
            e.AssetNumber +
            "&modifyFiled=" +
            this.info.ModifyFiled +
            "&type=detail&checkStatus=" +
            this.info.AssetCheckStatus,
        });
      }
    },
    click(e) {
      if (e.index == 0 || e.index == 1) {
        this.searchForm.CheckStatus = e.index;
        this.searchForm.AssetCheckStatus = "";
      } else {
        this.searchForm.CheckStatus = "";
        this.searchForm.AssetCheckStatus = e.index;
      }
      this.list = [];
      this.current = e.index;
      this.page = 1;
      this.loadmore.status = "loading";
      this[e.index == 3 ? "getProfitList" : "getList"](1).then((res) => {
        this.list = res;
      });
    },
    getList(page) {
      return new Promise((resolve) => {
        this.$apis
          .getAssetCheckDetailList(
            {
              PageSize: 10,
              PageIndex: page,
              AssetCheckCode: this.Code, //主键编码
              ...this.searchForm,
            },
            { custom: { loading: false } }
          )
          .then((res) => {
            if (res.data.length < 10 && this.current != 3) {
              this.loadmore.status = "nomore";
            }
            resolve(res.data);
          });
      });
    },
    getProfitList() {
      return new Promise((resolve) => {
        this.$apis
          .getAssetCheckProfitList({
            AssetCheckCode: this.Code,
            ...this.searchForm,
          })
          .then((res) => {
            if (res.data.length < 10 && this.current == 3) {
              this.loadmore.status = "nomore";
            }
            resolve(res.data);
          });
      });
    },
  },
  onReachBottom() {
    if (this.loadmore.status == "nomore") {
      return;
    }
    var list = this.list;
    this[this.current == 3 ? "getProfitList" : "getList"](this.page + 1).then((res) => {
      for (let index in res) {
        list.push(res[index]);
      }
      if (res.length > 0) {
        this.list = list;
        this.page++;
        if (res.length < 10) {
          this.loadmore.status = "nomore";
        }
      }
    });
  },
  onPageScroll(e) {
    this.scrollTop = e.scrollTop;
  },
};
</script>

<style></style>
