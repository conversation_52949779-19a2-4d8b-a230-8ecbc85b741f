<template>
  <view>
    <view class="hr"></view>
    <view class="pannel">
      <view class="tit bor-b">审批历史</view>
      <view class="his-line pd30">
        <!-- <view class="li flext" v-for="(item, index) in checkList">
          <view class="flex-hd">
            <view class="dot"></view>
            <view class="line"></view>
          </view>
          <view class="flex-bd con">
            <view class="flex tops">
              <view class="name flex-bd">{{ item.DealUserName }}</view>
              <view class="time">{{ item.DealDatetime }}</view>
            </view>
            <view class="flex flext">
              <view class="flex-bd desc">意见：{{ item.DealOption ? item.DealOption : "--" }}</view>
              <view class="status">{{ item.StatusName }}</view>
            </view>
          </view>
        </view> -->
        <view class="li flext" v-for="(item, index) in checkList">
          <view class="flex-hd">
            <view class="dot"></view>
            <view class="line"></view>
          </view>
          <view class="flex-bd">
            <view class="time">{{ item.DealDatetime }}</view>
            <view class="desc">
              <view class="p">
                审批人:
                <view class="inline">{{ item.DealUserName }}</view>
              </view>
              <view class="p">
                状态:
                <view class="inline">{{ item.StatusName }}</view>
              </view>
              <view class="p">
                意见:
                <view class="inline">{{ item.DealOption ? item.DealOption : "--" }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="hr"></view>
    <view class="pannel">
      <view class="tit bor-b">审批信息</view>
      <view class="pd30">
        <uv-steps :current="current" direction="column" :activeColor="$c.themeColor()">
          <uv-steps-item :title="item.NodeName + item.ReceiveUserName" :desc="item.DealOption" v-for="(item, index) in checkNode" :key="index">
            <view v-if="index < current" slot="icon">
              <view class="slot-icon">
                <view class="inline">
                  <uv-icon size="12" name="checkmark" color="#00b42a"></uv-icon>
                </view>
              </view>
            </view>
            <view v-if="index == current && currentStatus == 'fail'" slot="icon">
              <view class="slot-icon fail">
                <view class="inline">
                  <uv-icon size="12" name="close" color="#f53f3f"></uv-icon>
                </view>
              </view>
            </view>
          </uv-steps-item>
        </uv-steps>
      </view>
    </view>
    <view class="hr"></view>
  </view>
</template>

<script>
export default {
  name: "common-audit-progress",

  props: {
    code: {
      type: String,
      default: false,
    },
  },

  data() {
    return {
      checkList: [],
      checkNode: [],
      current: 0,
      currentStatus: "",
    };
  },

  watch: {
    value: {
      handler(val) {},
      immediate: true,
      deep: true,
    },
  },

  created() {
    //获取审批历史
    this.$apis
      .getAuditHistoryList({
        Code: this.code,
      })
      .then((res) => {
        this.checkList = res.data;
      });
    //获取当期当前审核进度
    this.$apis
      .getAuditProgress({
        Code: this.code,
      })
      .then((res) => {
        this.checkNode = res.data;
        var current = this.checkNode.findIndex((node) => node.DealStatus == "inprogress" || node.DealStatus == "fail");
        this.current = current == -1 ? res.data.length : current;
        this.currentStatus = this.checkNode[this.current] ? this.checkNode[this.current].DealStatus : "";
      });
  },

  methods: {},
};
</script>

<style lang="scss">
.slot-icon {
  width: 21px;
  height: 21px;
  border: 1px solid #00b42a;
  box-sizing: border-box;
  border-radius: 100px;
  font-size: 12px;
  line-height: 21px;
  text-align: center;
}
.slot-icon.fail {
  border: 1px solid #f53f3f;
}
</style>
