<template>
  <view>
    <uv-navbar title="我的" :fixed="true" :placeholder="true" :autoBack="false" :bgColor="$c.themeColor()" :titleStyle="{ color: '#fff' }">
      <template v-slot:left><view></view></template>
    </uv-navbar>
    <view class="user-top flex">
      <view class="user-avatar">
        <uv-avatar size="50" :src="UserInfo.src"></uv-avatar>
      </view>
      <view class="flex-bd">
        <view class="name">{{ UserInfo.username }}</view>
        <view class="logtime">
          <view class="inline">{{ UserInfo.DeptName }}</view>
        </view>
      </view>
    </view>

    <view class="user-main" style="margin-top: 20rpx">
      <view class="u-cells">
        <view class="cell bor-b" @click="$c.navigateTo('../userInfo/userInfo')">
          <view class="cell-icon">
            <image src="../../static/images/icon-yonghu.png"></image>
          </view>
          <view class="cell-content">用户信息</view>
          <view class="cell-ft">
            <uv-icon name="arrow-right" color="#999" :size="16"></uv-icon>
          </view>
        </view>
        <view class="cell bor-b" @click="$c.navigateTo('../editPwd/editPwd')">
          <view class="cell-icon">
            <image src="../../static/images/icon-juese.png"></image>
          </view>
          <view class="cell-content">修改密码</view>
          <view class="cell-ft">
            <uv-icon name="arrow-right" color="#999" :size="16"></uv-icon>
          </view>
        </view>
        <view class="cell bor-b" @click="checkUpdate">
          <view class="cell-icon">
            <image src="../../static/images/icon-setting.png"></image>
          </view>
          <view class="cell-content">版本号</view>
          <view class="cell-ft">
            <view class="inline">{{ version }}</view>
            <view class="inline"><uv-icon name="arrow-right" color="#999" :size="16"></uv-icon></view>
          </view>
        </view>
        <view class="cell" @click="syncConfig">
          <view class="cell-icon">
            <image src="../../static/images/icon-setting.png"></image>
          </view>
          <view class="cell-content">同步系统配置</view>
          <view class="cell-ft">
            <view class="inline"><uv-icon name="arrow-right" color="#999" :size="16"></uv-icon></view>
          </view>
        </view>
      </view>
    </view>
    <view style="margin: 30rpx 0 0 0">
      <uv-button :ripple="true" :hairline="false" customStyle="border:none;height:90rpx" type="info" @click="logout()">退出登录</uv-button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      UserInfo: "",
      version: "",
    };
  },
  onLoad() {
    this.UserInfo = uni.getStorageSync("UserInfo");
    // #ifdef APP
    plus.runtime.getProperty(plus.runtime.appid, (wgtinfo) => {
      this.version = Number(wgtinfo.versionCode);
    });
    // #endif
  },
  methods: {
    syncConfig() {
      //获取规则编码
      this.$apis.getRuleCodeList().then((res) => {
        if (res.code == 900) {
          var arr = [];
          for (let i in res.data) {
            arr.push({
              code: res.data[i].Code,
              isAuto: res.data[i].IsAuto,
            });
          }
          uni.setStorageSync("ruleCode", arr);
          uni.$uv.toast("同步成功");
        }
      });
    },
    logout() {
      uni.removeStorageSync("UserInfo");
      uni.removeStorageSync("UserCode");
      uni.reLaunch({
        url: "../login/login",
      });
    },
    checkUpdate() {
      // #ifdef APP
      uni.request({
        url: "https://annyou.oss-cn-shenzhen.aliyuncs.com/amsapp/check.json",
        success: (res) => {
          var wgt = res.data.wgt;
          if (res.data.version > this.version) {
            uni.showModal({
              title: "提示",
              content: "有新版本，是否更新？",
              success: (res) => {
                if (res.confirm) {
                  this.down(wgt);
                }
              },
            });
          } else {
            uni.$uv.toast("当前已是最新版本");
          }
        },
      });
      // #endif
    },
    down(e) {
      var dtask = plus.downloader.createDownload(e, {}, function (d, status) {
        // 下载完成
        if (status == 200) {
          plus.runtime.install(
            d.filename,
            {
              force: true,
            },
            function () {
              uni.showToast({
                title: "更新完成",
                mask: false,
                duration: 1500,
              });
              setTimeout(function () {
                plus.runtime.restart();
              }, 1600);
            },
            function (error) {
              uni.showToast({
                title: "更新失败-01",
                mask: false,
                duration: 1500,
              });
            }
          );
        } else {
          uni.showToast({
            title: "更新失败-02",
            mask: false,
            duration: 1500,
          });
        }
      });
      try {
        dtask.start(); // 开启下载的任务
        var prg = 0;
        var showLoading = plus.nativeUI.showWaiting("正在下载"); //创建一个showWaiting对象
        dtask.addEventListener("statechanged", function (task, status) {
          // 给下载任务设置一个监听 并根据状态  做操作
          switch (task.state) {
            case 1:
              showLoading.setTitle("正在下载");
              break;
            case 2:
              showLoading.setTitle("已连接到服务器");
              break;
            case 3:
              prg = parseInt((parseFloat(task.downloadedSize) / parseFloat(task.totalSize)) * 100);
              showLoading.setTitle("  正在下载" + prg + "%  ");
              break;
            case 4:
              plus.nativeUI.closeWaiting();
              //下载完成
              break;
          }
        });
      } catch (err) {
        plus.nativeUI.closeWaiting();
        uni.showToast({
          title: "更新失败-03",
          mask: false,
          duration: 1500,
        });
      }
    },
  },
};
</script>

<style>
page {
  background: #f7f7f7;
}
</style>
