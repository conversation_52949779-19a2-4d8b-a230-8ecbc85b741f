<template>
  <view>
    <uv-navbar title="资产入库" :fixed="true" :placeholder="true" :autoBack="true" :bgColor="$c.themeColor()" leftIconColor="#ffffff" :titleStyle="{ color: '#fff' }"></uv-navbar>
    <view class="pd-info">
      <view class="pannel">
        <view class="tit bor-b flex">
          <view class="flex-bd">单据信息</view>
          <view class="flex-hd">
            <view :class="'tag check-tag' + info.WfStatus">{{ info.WfStatusCaption }}</view>
          </view>
        </view>
        <view class="pd030">
          <!-- 易耗品入库 -->
          <view class="form-cell-group">
            <view class="form-cell flex bor-b">
              <view class="cell-label">单据号</view>
              <view class="cell-value">{{ info.BillNo }}</view>
            </view>
            <view class="form-cell flex bor-b">
              <view class="cell-label">申请标题</view>
              <view class="cell-value">{{ info.ApplyTitle }}</view>
            </view>
            <view class="form-cell flex bor-b">
              <view class="cell-label">经办人</view>
              <view class="cell-value">{{ info.ReceiptUserName }}</view>
            </view>
            <view class="form-cell flex bor-b">
              <view class="cell-label">入库日期</view>
              <view class="cell-value">{{ info.ReceiptDateTimeCaption }}</view>
            </view>
            <view class="form-cell flex">
              <view class="cell-label">备注</view>
              <view class="cell-value">{{ info.Remark }}</view>
            </view>
          </view>
        </view>
      </view>
      <view class="hr"></view>
      <view class="pannel">
        <view class="tit flex bor-b">
          <view class="flex-bd">单据详情</view>
        </view>
        <view class="pd030">
          <view class="sp-eList">
            <view class="li" v-for="(item, index) in assetList">
              <view class="t">{{ item.AssetName }}</view>
              <view class="lii flex">
                <view class="flex-hd">资产编号</view>
                <view class="flex-bd">{{ item.AssetNumber || "--" }}</view>
              </view>
              <view class="lii flex">
                <view class="flex-hd">数量</view>
                <view class="flex-bd">{{ item.Amount || "--" }}</view>
              </view>
              <view class="lii flex">
                <view class="flex-hd">资产分类</view>
                <view class="flex-bd">{{ item.CateName || "--" }}</view>
              </view>
              <view class="lii flex">
                <view class="flex-hd">管理部门</view>
                <view class="flex-bd">{{ item.OrgName || "--" }}</view>
              </view>
              <view class="lii flex">
                <view class="flex-hd">供应商名称</view>
                <view class="flex-bd">{{ item.SupplierName || "--" }}</view>
              </view>
              <view class="lii flex">
                <view class="flex-hd">品牌名称</view>
                <view class="flex-bd">{{ item.BrandName || "--" }}</view>
              </view>
              <view class="lii flex">
                <view class="flex-hd">仓库</view>
                <view class="flex-bd">{{ item.WarehouseName || "--" }}</view>
              </view>
              <view class="lii flex">
                <view class="flex-hd">规格型号</view>
                <view class="flex-bd">{{ item.Specifications || "--" }}</view>
              </view>
              <view class="lii flex">
                <view class="flex-hd">单位</view>
                <view class="flex-bd">{{ item.Unit || "--" }}</view>
              </view>
              <view class="lii flex">
                <view class="flex-hd">位置</view>
                <view class="flex-bd">{{ item.NamePath || "--" }}</view>
              </view>
              <view class="lii flex">
                <view class="flex-hd">购置日期</view>
                <view class="flex-bd">{{ item.BuyDateCaption || "--" }}</view>
              </view>
              <view class="lii flex">
                <view class="flex-hd">使用期限</view>
                <view class="flex-bd">{{ item.ScrapMonth != null ? item.ScrapMonth : "--" }}</view>
              </view>
              <view class="lii flex">
                <view class="flex-hd">资产价值</view>
                <view class="flex-bd">{{ item.AssetPrice != null ? item.AssetPrice : "--" }}</view>
              </view>
              <view class="lii flex">
                <view class="flex-hd">来源</view>
                <view class="flex-bd">{{ item.SourceTypeCaption || "--" }}</view>
              </view>
              <view class="lii flex">
                <view class="flex-hd">备注</view>
                <view class="flex-bd">{{ item.Remark || "--" }}</view>
              </view>
              <view class="lii flex flext">
                <view class="flex-hd">资产图片</view>
                <view class="flex-bd">
                  <image v-if="item.Smallimage" :src="$c.getImageUrl(item.Smallimage)" mode="aspectFill" class="asset-img" @click="$c.viewImage(item.Smallimage)" />
                  <text v-else>--</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <common-audit-progress :code="code" v-if="info.WfStatus > -1"></common-audit-progress>
    </view>
    <view class="fixbtn" v-if="info.WfStatus == -1 || info.WfStatus == 2">
      <view class="blank"></view>
      <view class="btns bor-t flex">
        <view class="btn btn-bor red" @click="del()">删除</view>
        <view class="btn btn-bor" @click="$c.navigateTo('../assetsReceipt/add?type=edit&Code=' + info.Code)">修改</view>
        <view class="btn" @click="submit()">提交</view>
      </view>
    </view>
    <common-approve :code="code" :taskCode="taskCode" api="approveAssetReceipt" v-if="info.WfStatus == 0 && type == 'audit'" />
  </view>
</template>

<script>
export default {
  data() {
    return {
      info: "",
      assetList: "",
      type: "",
      code: "",
      taskCode: "",
    };
  },
  onLoad(e) {
    this.code = e.Code;
    this.type = e.type;
    this.taskCode = e.taskCode;
    this.detail();

    uni.$on("refreshDetail", () => {
      this.detail();
    });
  },
  onUnload() {
    uni.$off("refreshDetail");
  },
  methods: {
    detail() {
      this.$apis
        .getAssetReceiptDetail({
          Code: this.code,
        })
        .then((res) => {
          this.info = res.data.AssetReceipt;
          this.assetList = res.data.AssetReceiptDetail;
        });
    },
    submit() {
      this.$c.submitAsset(this.info.Code, "submitAssetReceipt");
    },

    del() {
      uni.showModal({
        title: "提示",
        content: "确定删除吗？",
        success: (res) => {
          if (res.confirm) {
            this.$apis
              .delAssetReceipt({
                Code: this.code,
              })
              .then((res) => {
                if (res.code === 103) {
                  uni.$uv.toast("删除成功");
                  setTimeout(() => {
                    uni.navigateBack();
                    uni.$emit("refreshList");
                  }, 1500);
                } else {
                  uni.$uv.toast(res.msg || "删除失败");
                }
              });
          }
        },
      });
    },
  },
};
</script>

<style></style>
