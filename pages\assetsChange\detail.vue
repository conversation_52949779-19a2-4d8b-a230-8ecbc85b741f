<template>
  <view>
    <uv-navbar title="资产变更" :fixed="true" :placeholder="true" :autoBack="true" :bgColor="$c.themeColor()" leftIconColor="#ffffff" :titleStyle="{ color: '#fff' }"></uv-navbar>
    <view class="pd-info">
      <view class="pannel">
        <view class="tit bor-b flex">
          <view class="flex-bd">单据信息</view>
          <view class="flex-hd">
            <view :class="'tag check-tag' + info.WfStatus">{{ info.WfStatusCaption }}</view>
          </view>
        </view>
        <view class="pd030">
          <!-- 易耗品入库 -->
          <view class="form-cell-group">
            <view class="form-cell flex bor-b">
              <view class="cell-label">单据号</view>
              <view class="cell-value">{{ info.BillNo }}</view>
            </view>
            <view class="form-cell flex bor-b">
              <view class="cell-label">申请标题</view>
              <view class="cell-value">{{ info.ApplyTitle }}</view>
            </view>
            <view class="form-cell flex bor-b">
              <view class="cell-label">经办人</view>
              <view class="cell-value">{{ info.HandleUserName }}</view>
            </view>
            <view class="form-cell flex bor-b">
              <view class="cell-label">变更日期</view>
              <view class="cell-value">{{ info.ApplyDateCaption }}</view>
            </view>
            <view class="form-cell flex">
              <view class="cell-label">备注</view>
              <view class="cell-value">{{ info.Remark }}</view>
            </view>
          </view>
        </view>
      </view>
      <view class="hr"></view>
      <view class="pannel">
        <view class="tit flex bor-b">
          <view class="flex-bd">单据详情</view>
        </view>
        <view class="pd030">
          <view class="sp-eList">
            <view class="li" v-for="(item, index) in assetList">
              <view class="t">{{ item.AssetName }}</view>
              <view class="lii flex">
                <view class="flex-hd">资产编号</view>
                <view class="flex-bd">{{ item.AssetNumber || "--" }}</view>
              </view>
              <view class="lii flex">
                <view class="flex-hd">管理部门</view>
                <view class="flex-bd">{{ item.AssetOrgName || "--" }}</view>
              </view>
              <view class="lii flex">
                <view class="flex-hd">使用部门</view>
                <view class="flex-bd">{{ item.UseDeptName || "--" }}</view>
              </view>
              <view class="lii flex">
                <view class="flex-hd">使用人</view>
                <view class="flex-bd">{{ item.UseUserName || "--" }}</view>
              </view>
              <view class="lii flex">
                <view class="flex-hd">位置</view>
                <view class="flex-bd">{{ item.LocationInfoName || "--" }}</view>
              </view>
              <view class="lii flex">
                <view class="flex-hd">管理人</view>
                <view class="flex-bd">{{ item.ChargeUserName || "--" }}</view>
              </view>
              <view class="lii flex">
                <view class="flex-hd">购置日期</view>
                <view class="flex-bd">{{ item.BuyDateCaption || "--" }}</view>
              </view>
              <view class="lii flex">
                <view class="flex-hd">使用日期</view>
                <view class="flex-bd">{{ item.UseDateCaption || "--" }}</view>
              </view>
              <view class="lii flex">
                <view class="flex-hd">使用期限(月)</view>
                <view class="flex-bd">{{ item.ScrapMonth || "--" }}</view>
              </view>
              <view class="lii flex">
                <view class="flex-hd">规格型号</view>
                <view class="flex-bd">{{ item.Specification || "--" }}</view>
              </view>
              <view class="lii flex">
                <view class="flex-hd">计量单位</view>
                <view class="flex-bd">{{ item.Unit || "--" }}</view>
              </view>
              <view class="lii flex">
                <view class="flex-hd">供应商</view>
                <view class="flex-bd">{{ item.SupplierName || "--" }}</view>
              </view>
              <view class="lii flex">
                <view class="flex-hd">品牌</view>
                <view class="flex-bd">{{ item.BrandName || "--" }}</view>
              </view>
              <view class="lii flex">
                <view class="flex-hd">资产来源</view>
                <view class="flex-bd">{{ item.SourceTypeCaption || "--" }}</view>
              </view>
              <view class="lii flex">
                <view class="flex-hd">资产状态</view>
                <view class="flex-bd">{{ item.AssetStatusNameCaption || "--" }}</view>
              </view>
              <view class="lii flex">
                <view class="flex-hd">财政编号</view>
                <view class="flex-bd">{{ item.FinanceNumber || "--" }}</view>
              </view>
              <view class="lii flex flext">
                <view class="flex-hd">资产图片</view>
                <view class="flex-bd">
                  <image v-if="item.Smallimage" :src="$c.getImageUrl(item.Smallimage)" mode="aspectFill" class="asset-img" @click="$c.viewImage(item.Smallimage)" />
                  <text v-else>--</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <common-audit-progress :code="code" v-if="info.WfStatus > -1" />
    </view>
    <view class="fixbtn" v-if="info.WfStatus == -1 || info.WfStatus == 2">
      <view class="blank"></view>
      <view class="btns bor-t flex">
        <view class="btn btn-bor red" @click="del()">删除</view>
        <view class="btn btn-bor" @click="$c.navigateTo('../assetsChange/add?type=edit&Code=' + info.Code)">修改</view>
        <view class="btn" @click="submit()">提交</view>
      </view>
    </view>
    <common-approve :code="code" :taskCode="taskCode" api="approveAssetChange" v-if="info.WfStatus == 0 && type == 'audit'" />
  </view>
</template>

<script>
export default {
  data() {
    return {
      info: "",
      assetList: "",
      type: "",
      code: "",
      taskCode: "",
    };
  },
  onLoad(e) {
    this.code = e.Code;
    this.type = e.type;
    this.taskCode = e.taskCode;
    this.detail();

    uni.$on("refreshDetail", () => {
      this.detail();
    });
  },
  onUnload() {
    // 页面销毁时取消事件监听
    uni.$off("refreshDetail");
  },
  methods: {
    detail() {
      this.$apis
        .getAssetChangeDetail({
          Code: this.code,
        })
        .then((res) => {
          this.info = res.data.AssetChange;
          this.assetList = res.data.AssetChangeDetail;
        });
    },
    submit() {
      this.$c.submitAsset(this.info.Code, "submitAssetChange");
    },
    del() {
      uni.showModal({
        title: "提示",
        content: "确定删除吗？",
        success: (res) => {
          if (res.confirm) {
            this.$apis
              .delAssetChange({
                Code: this.code,
              })
              .then((res) => {
                if (res.code === 103) {
                  uni.$uv.toast("删除成功");
                  setTimeout(() => {
                    uni.navigateBack();
                    uni.$emit("refreshList");
                  }, 1500);
                } else {
                  uni.$uv.toast(res.msg || "删除失败");
                }
              });
          }
        },
      });
    },
  },
};
</script>

<style lang="scss"></style>
