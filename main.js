import App from "./App";
import uvUI from "@/uni_modules/uv-ui-tools";
import common from "./common/common.js";
import apis from "./common/apis.js";

//组件
import commonApprove from "@/components/common-approve/common-approve.vue";
import commonAuditProgress from "@/components/common-audit-progress/common-audit-progress.vue";
import commonFilter from "@/components/common-filter/common-filter.vue";
import commonPicker from "@/components/common-picker/common-picker.vue";
import commonUpload from "@/components/common-upload/common-upload.vue";

// 基础配置
const appConfig = {
  theme: {
    logo: "/static/annyou-logo.png",
    title: "固定资产管理系统",
    subTitle: "欢迎登录",
    themeColor: "#1677ff", // 主题色
    backgroundImage: "/static/images/loginbg.jpg", // 背景图
  },
};

// #ifdef H5
import Vconsole from "vconsole";
// 仅在开发或测试环境启用vconsole
let vConsole = null;
if (process.env.NODE_ENV === "development" || process.env.VUE_APP_TITLE === "test") {
  //vConsole = new Vconsole();
}
// #endif

// #ifndef VUE3
import Vue from "vue";
import "./uni.promisify.adaptor";

// Vue基础配置
Vue.config.productionTip = false;
App.mpType = "app";

// 全局属性
Vue.prototype.$c = common;
Vue.prototype.$apis = apis;
Vue.prototype.$theme = appConfig.theme;


Vue.component("common-approve", commonApprove);
Vue.component("common-audit-progress", commonAuditProgress);
Vue.component("common-filter", commonFilter);
Vue.component("common-picker", commonPicker);
Vue.component("common-upload", commonUpload);
// 创建Vue实例
const app = new Vue({
  ...App,
});
app.$mount();

// 解决uniapp uni.$on重复执行的问题
const originalOn = uni.$on;
uni.$on = (eventName, callback) => {
  try {
    uni.$off(eventName);
  } catch (error) {
    // 忽略错误
  }
  originalOn(eventName, callback);
};
// #endif

// #ifdef VUE3
import { createSSRApp } from "vue";
export function createApp() {
  const app = createSSRApp(App);

  // 这里可以添加Vue3的全局配置
  app.config.globalProperties.$c = common;
  app.config.globalProperties.$apis = apis;
  app.config.globalProperties.$theme = appConfig.theme;

  // 使用插件
  //app.use(uView);
  app.component("common-approve", commonApprove);
  app.component("common-audit-progress", commonAuditProgress);
  app.component("common-filter", commonFilter);
  app.component("common-picker", commonPicker);
  app.component("common-upload", commonUpload);
  return {
    app,
  };
}
// #endif

// #ifdef H5
export { vConsole };
// #endif
