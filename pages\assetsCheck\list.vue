<template>
  <view>
    <uv-navbar title="资产盘点" :fixed="true" :placeholder="true" :autoBack="true" :bgColor="$c.themeColor()" leftIconColor="#ffffff" :titleStyle="{ color: '#fff' }"></uv-navbar>
    <view class="fixed-tab" :style="'top: ' + divTop + 'px;'">
      <uv-tabs :list="tabs" @click="click" :scrollable="false"></uv-tabs>
    </view>
    <view class="scroll" style="padding-top: 44px">
      <view class="pd-list">
        <view class="li" v-for="(item, index) in list" @click="$c.navigateTo('../assetsCheck/detail?Code=' + item.Code + '&type=detail')">
          <view class="name flex bor-b">
            <view class="flex-bd">
              {{ item.BillNo }}
            </view>
            <view class="tag check-tag-1" v-if="item.AssetCheckStatus == 0">未盘点</view>
            <view class="tag check-tag1" v-if="item.AssetCheckStatus == 1">已盘点</view>
          </view>
          <view class="info">
            <view class="i flex">
              <view class="span">盘点主题</view>
              <view class="flex-bd line1">{{ item.CheckTitle }}</view>
            </view>
            <view class="i flex">
              <view class="span">创建人</view>
              <view class="flex-bd line1">{{ item.CreateUserNameCaption }}</view>
            </view>
            <view class="i flex">
              <view class="span">创建日期</view>
              <view class="flex-bd line1">{{ item.CreateDatetimeCaption }}</view>
            </view>
            <view class="i flex">
              <view class="span">管理员</view>
              <view class="flex-bd line1">{{ item.CheckUserNameCaption }}</view>
            </view>
          </view>
          <!-- <view class="counts">
            <view class="i">
              <view class="span">总数</view>
              <view class="n">{{ item.DealNum + item.NoDealNum }}</view>
            </view>
            <view class="i">
              <view class="span">已盘</view>
              <view class="n">{{ item.DealNum }}</view>
            </view>
            <view class="i">
              <view class="span">未盘</view>
              <view class="n">{{ item.NoDealNum }}</view>
            </view>
          </view>
          
          <view class="btns bor-t" v-if="appType == 'pda'">
            <view class="btn bor-l" @click="pand(item)">离线盘点</view>
          </view> -->
          <view class="btns bor-t">
            <view class="btn" @click.stop="endShow(item.Code)">结束盘点</view>
            <view class="btn bor-l" @click.stop="$c.navigateTo('../assetsCheck/checkList?Code=' + item.Code)">在线盘点</view>
          </view>
        </view>
      </view>
    </view>
    <view class="pd30">
      <uv-load-more
        fontSize="12"
        color="#999"
        :status="loadmore.status"
        :loading-text="loadmore.loadingText"
        :loadmore-text="loadmore.loadmoreText"
        :nomore-text="loadmore.nomoreText" />
    </view>
    <uv-back-top
      :scroll-top="scrollTop"
      mode="square"
      :iconStyle="{
        fontSize: '32rpx',
        color: '#fff',
      }"
      :customStyle="'background-color:' + $c.themeColor()"></uv-back-top>
    <uv-modal ref="endModel" :showCancelButton="true" @confirm="endCheck" @cancel="endCheckShow = false">
      <rich-text :nodes="content"></rich-text>
    </uv-modal>
  </view>
</template>

<script>
// #ifdef APP-PLUS
var pdamodule = uni.requireNativePlugin("uniplugin_pdamodule");
// #endif
export default {
  data() {
    return {
      tabs: [{ name: "未盘点" }, { name: "已盘点" }],
      divTop: 0,
      scrollTop: 0,
      loadmore: {
        status: "loading",
        loadingText: "努力加载中",
        loadmoreText: "轻轻上拉",
        nomoreText: "没有更多了",
      },
      searchForm: {
        PageSize: 10,
        LoginUserCode: uni.getStorageSync("UserCode"),
        CheckTitle: "",
        BillNo: "",
        CreateDatetime: "",
        AssetCheckStatus: 0,
        WfStatus: 1,
      },
      list: [],
      page: 1,
      endCheckShow: false,
      endCheckCode: "",
      content: '<p style="text-align:center;color:#666">确认结束盘点？</p>',
    };
  },
  onLoad() {
    var sys = uni.getSystemInfoSync();
    this.divTop = sys.statusBarHeight + 44;

    this.getList(1).then((res) => {
      this.list = res;
    });

    uni.$on("refreshList", () => {
      this.page = 1;
      this.loadmore.status = "loading";
      this.getList(1).then((res) => {
        this.list = res;
      });
    });
  },
  onUnload() {
    // 页面销毁时取消事件监听
    uni.$off("refreshList");
  },
  methods: {
    endShow(code) {
      console.log(code);
      this.$refs.endModel.open();
      this.endCheckCode = code;
    },
    endCheck() {
      this.$refs.endModel.close();
      this.$apis
        .endAssetCheck({
          AssetCheckCode: this.endCheckCode, //盘点编码
          LoginUserCode: uni.getStorageSync("UserCode"),
        })
        .then((res) => {
          uni.$uv.toast(res.msg);
          if (res.code == 100) {
            this.page = 1;
            this.loadmore.status = "loading";
            this.getList(1).then((res) => {
              this.list = res;
            });
          }
        });
    },
    click(e) {
      this.searchForm.AssetCheckStatus = e.index;
      this.page = 1;
      this.loadmore.status = "loading";
      this.getList(1).then((res) => {
        this.list = res;
      });
    },
    getList(page) {
      return new Promise((resolve) => {
        this.$apis
          .getAssetCheckList(
            {
              PageIndex: page,
              ...this.searchForm,
            },
            { custom: { loading: false } }
          )
          .then((res) => {
            if (res.data.length < 10) {
              this.loadmore.status = "nomore";
            }
            resolve(res.data);
          });
      });
    },
  },
  onReachBottom() {
    if (this.loadmore.status == "nomore") {
      return;
    }
    var list = this.list;
    this.getList(this.page + 1).then((res) => {
      for (let index in res) {
        list.push(res[index]);
      }
      if (res.length > 0) {
        this.list = list;
        this.page++;
        if (res.length < 10) {
          this.loadmore.status = "nomore";
        }
      }
    });
  },
  onPageScroll(e) {
    this.scrollTop = e.scrollTop;
  },
  onPullDownRefresh() {
    this.page = 1;
    this.loadmore.status = "loading";
    this.getList(1).then((res) => {
      this.list = res;
      uni.$uv.toast("刷新成功");
      uni.stopPullDownRefresh();
    });
  },
};
</script>

<style>
page {
  background: #f7f7f7;
}
</style>
