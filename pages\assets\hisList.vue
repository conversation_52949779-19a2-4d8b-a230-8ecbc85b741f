<template>
  <view>
    <uv-navbar title="资产履历" :fixed="true" :placeholder="true" :autoBack="true" :bgColor="$c.themeColor()" leftIconColor="#ffffff" :titleStyle="{ color: '#fff' }"></uv-navbar>
    <view class="pd-info">
      <view class="pannel">
        <view class="his-line pd30">
          <view class="li flext" v-for="(item, index) in hisList">
            <view class="flex-hd">
              <view class="dot"></view>
              <view class="line" v-if="index !== hisList.length - 1"></view>
            </view>
            <view class="flex-bd">
              <view class="time">{{ $c.formatDate(item.CreateDateTime) }}</view>
              <view class="desc">
                <view class="p">
                  操作人员:
                  <view class="inline">{{ item.UserName }}</view>
                </view>
                <view class="p">操作内容: {{ item.OperateContent }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      hisList: [],
    };
  },
  onLoad(options) {
    this.getAssetHistoryList(options.code);
  },
  methods: {
    getAssetHistoryList(code) {
      this.$apis.getAssetHistoryList({ Code: code }).then((res) => {
        this.hisList = res.data;
      });
    },
  },
};
</script>

<style></style>
