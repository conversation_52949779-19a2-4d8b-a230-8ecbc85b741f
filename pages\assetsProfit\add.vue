<template>
  <view>
    <uv-navbar title="资产盘盈" :fixed="true" :placeholder="true" :autoBack="true" :bgColor="$c.themeColor()" leftIconColor="#ffffff" :titleStyle="{ color: '#fff' }"></uv-navbar>
    <view class="pd-info">
      <view class="pannel">
        <view class="tit bor-b flex">
          <view class="flex-bd">资产信息</view>
          <!-- <view class="flex-hd"><u-button type="primary" :customStyle="{ height: '50rpx', background: $c.themeColor() }" @click="add()">增加</u-button></view> -->
        </view>
        <view class="pd030 zi-card" v-for="(item, index) in form.AssetCheckMoreDetail" :key="index">
          <uv-form labelWidth="80" :labelStyle="{ fontSize: '28rpx' }">
            <block v-for="(i, idx) in asset">
              <uv-form-item v-if="i.type === 'input'" :label="i.desc" :borderBottom="idx < asset.length - 1" :required="i.required">
                <uv-input v-model="item[i.name]" border="none" placeholder="请输入" />
                <uv-button v-if="idx == 0" slot="right" type="info" customStyle="height:50rpx;font-size:24rpx;" @click="del(index)">删除</uv-button>
                <view v-if="i.ft" slot="right">{{ i.ft }}</view>
              </uv-form-item>
              <uv-form-item v-if="i.type === 'date'" :label="i.desc" :borderBottom="idx < asset.length - 1" :required="i.required">
                <uni-datetime-picker type="date" v-model="item[i.name]" :border="false" :end="maxDate" :clear-icon="false"></uni-datetime-picker>
                <uv-icon slot="right" name="arrow-right"></uv-icon>
              </uv-form-item>
              <block v-if="i.type.includes('picker')">
                <common-picker
                  :borderBottom="idx < asset.length - 1"
                  :required="i.required"
                  :label="i.desc"
                  :pickerType="i.pickerType"
                  :modelValue="item[i.value]"
                  :displayValue="item[i.name]"
                  :condition="i.condition ? { field: i.condition, value: item[i.watchValue] } : null"
                  :watchValue="i.watchValue ? item[i.watchValue] : null"
                  @update:modelValue="(val) => updateValue(index, i.value, val)"
                  @update:displayValue="(val) => updateDisplayValue(index, i.name, val)"></common-picker>
              </block>
              <uv-form-item v-if="i.type === 'upload'" :label="i.desc" :borderBottom="idx < asset.length - 1" :required="i.required">
                <common-upload v-model="item[i.name]" :maxCount="1"></common-upload>
              </uv-form-item>
            </block>
          </uv-form>
        </view>
        <view class="pd30" v-if="form.AssetCheckMoreDetail.length == 0">
          <uv-empty :iconSize="70"></uv-empty>
        </view>
      </view>
    </view>
    <view class="fixed-add" @click="add()" style="bottom: 200rpx" v-if="type == 'add'">
      <view class="inline"><uv-icon name="plus" :size="20" color="#fff"></uv-icon></view>
    </view>
    <view class="fixbtn">
      <view class="blank"></view>
      <view class="btns bor-t flex">
        <view class="btn" @click="save()">保存</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      form: {
        LoginUserCode: uni.getStorageSync("UserCode"), //登录人
        AssetCheckCode: "", //盘点编码
        AssetCheckMoreDetail: [],
      },
      type: "",
      maxDate: new Date().getTime(),
      asset: [
        { name: "AssetName", desc: "资产名称", type: "input", required: true },
        { name: "AssetCateName", value: "AssetCateCode", desc: "资产分类", type: "picker", pickerType: "category", required: true },
        { name: "OrgName", value: "OrgCode", desc: "管理部门", type: "picker", pickerType: "dept", required: true },
        { name: "BuyDate", desc: "购置日期", type: "date", required: true },
        { name: "AssetPrice", desc: "资产价值", type: "input", inputType: "number", required: true },
        { name: "AssetNumber", desc: "资产编码", type: "hidden" },
        { name: "SupplierName", value: "SupplierCode", desc: "供应商", type: "picker", pickerType: "supplier" },
        { name: "BrandName", value: "BrandCode", desc: "品牌", type: "picker", pickerType: "brand" },
        { name: "WarehouseName", value: "WarehouseCode", desc: "仓库", type: "picker", pickerType: "warehouse" },
        { name: "Specifications", desc: "规格型号", type: "input" },
        { name: "Unit", desc: "单位", type: "input" },

        { name: "LocationName", value: "LocationCode", desc: "位置", type: "picker", pickerType: "location" },
      ],
    };
  },
  onLoad(e) {
    this.type = e.type;
    this.form.AssetCheckCode = e.AssetCheckCode;

    if (e.type == "edit") {
      this.$apis.getAssetCheckProfitDetail({ Code: e.detailCode }).then((res) => {
        var detail = res.data;
        var arr = [];
        var newItem = {};
        this.asset.forEach((field) => {
          if (detail[field.name]) {
            newItem[field.name] = detail[field.name];
          } else {
            newItem[field.name] = "";
            console.log(detail.AssetName, field.name);
          }
          if (field.value) {
            if (detail[field.value]) {
              newItem[field.value] = detail[field.value];
            } else {
              newItem[field.value] = "";
              console.log(detail.AssetName, field.value);
            }
          }
          newItem.SourceTypeCaption = detail.SourceTypeCaption;
          newItem.LocationName = detail.LocationInfoName;
          newItem.BuyDate = detail.BuyDateCaption;
          newItem.Code = e.detailCode;
        });
        arr.push(newItem);
        console.log(arr);
        this.form.AssetCheckMoreDetail = arr;
      });
    } else {
      this.add();
    }
  },
  methods: {
    updateValue(index, fieldName, value) {
      console.log(index, fieldName, value);
      this.form.AssetCheckMoreDetail[index][fieldName] = value;
    },
    updateDisplayValue(index, fieldName, value) {
      console.log(index, fieldName, value);
      this.form.AssetCheckMoreDetail[index][fieldName] = value;
    },
    add() {
      const newAsset = {};
      this.asset.forEach((field) => {
        newAsset[field.name] = "";
        if (field.value) {
          newAsset[field.value] = "";
        }
      });
      this.$set(this.form.AssetCheckMoreDetail, this.form.AssetCheckMoreDetail.length, newAsset);
    },
    del(index) {
      this.form.AssetCheckMoreDetail.splice(index, 1);
    },
    save() {
      // 遍历验证每个资产的必填字段
      for (let i = 0; i < this.form.AssetCheckMoreDetail.length; i++) {
        const item = this.form.AssetCheckMoreDetail[i];

        // 遍历资产字段定义，检查必填项
        for (const field of this.asset) {
          if (field.required) {
            // 根据字段类型判断值的位置
            const value = field.value ? item[field.value] : item[field.name];
            if (value === "" || value === null || value === undefined) {
              return uni.$uv.toast(`第${i + 1}个资产的${field.desc}不能为空`);
            }
          }
        }
      }

      // 所有验证通过，调用保存接口
      this.$apis[this.type == "add" ? "addAssetCheckProfit" : "editAssetCheckProfit"](this.form).then((res) => {
        if (res.code === 100) {
          uni.$uv.toast("保存成功");
          setTimeout(() => {
            uni.navigateBack();
            if (this.type == "add") {
              uni.$emit("refreshProfitList");
            } else {
              uni.$emit("refreshProfitDetail");
            }
          }, 1500);
        } else {
          uni.$uv.toast(res.msg || "保存失败");
        }
      });
    },
  },
};
</script>

<style></style>
